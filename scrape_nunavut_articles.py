import requests
from bs4 import BeautifulSoup
import csv
import time
import re
import os
import json
import html
from urllib.parse import urljoin, quote

# Configuration
BASE_URL = "https://recherche-collection-search.bac-lac.gc.ca"
SEARCH_URL = "https://recherche-collection-search.bac-lac.gc.ca/eng/Home/Result?q_type_1=q&q_1=Nunavut&SEARCH_TYPE=SEARCH_BASIC&num=25&start=16125&enviro=prod&onlinecode=1&"
OUTPUT_FILE = "scraped_recherche_data.csv"
DELAY = 5  # Delay between requests in seconds to be respectful

# Add headers for all requests to simulate a browser
DEFAULT_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
}

# CSV field names matching the provided format
FIELDNAMES = [
    "name", "place", "hierarchical level", "date", "item no. (creator)",
    "type of material", "reference", "found in", "item id number",
    "place of creation", "extent", "language of material", "scope and content",
    "additional name(s)", "finding aid no.", "additional information", "source",
    "other accession no", "subject heading", "conditions of access", "terms of use",
    "image", "url"
]

def get_page(url, max_retries=5, initial_backoff=5):
    """Get webpage content with proper headers, with retry logic"""
    session = requests.Session()

    # First, visit the main page to get cookies and establish a session
    try:
        session.get(BASE_URL, headers=DEFAULT_HEADERS, timeout=30)
    except requests.exceptions.RequestException:
        # If we can't connect to the main page, try to continue anyway
        pass

    # Try to get the actual page with exponential backoff for retries
    backoff = initial_backoff
    for retry in range(max_retries):
        try:
            response = session.get(url, headers=DEFAULT_HEADERS, timeout=30)
            response.raise_for_status()  # Raise an exception for 4xx/5xx responses

            print(f"Successfully fetched page: {url} (Status code: {response.status_code})")
            return response.text
        except requests.exceptions.RequestException as e:
            if retry == max_retries - 1:  # Last retry
                print(f"Error fetching {url} after {max_retries} attempts: {e}")
                return None

            print(f"Error fetching {url} (attempt {retry+1}/{max_retries}): {e}")
            print(f"Waiting {backoff} seconds before retrying...")
            time.sleep(backoff)

            # Increase backoff time for next retry (exponential backoff)
            backoff = min(backoff * 2, 120)  # Cap at 2 minutes

            # If it's a connection reset error, wait a bit longer
            if "Connection reset by peer" in str(e):
                print("Connection reset by server. This might be due to rate limiting.")
                extra_wait = 30
                print(f"Waiting an additional {extra_wait} seconds...")
                time.sleep(extra_wait)

    return None

def extract_search_results(html):
    """Extract search result URLs from the search results page"""
    soup = BeautifulSoup(html, 'html.parser')
    results = []

    # Print the first part of the HTML to debug
    print("HTML snippet for debugging:")
    html_snippet = html[:1000] if html else "No HTML content"
    print(html_snippet)

    # Try different selectors that might contain result items
    possible_selectors = [
        '.search-result-item', '.record-item', '.result-item',
        '.item', 'div.result', '.col-md-12 a', '.row a',
        'tbody tr', '.result-list-item'
    ]

    # Try each selector
    for selector in possible_selectors:
        result_items = soup.select(selector)
        if result_items:
            print(f"Found {len(result_items)} items with selector: {selector}")

            # Check all links in the found items
            for item in result_items:
                # Look for links either directly or within the item
                links = item.find_all('a', href=True) if hasattr(item, 'find_all') else []
                if hasattr(item, 'get') and item.get('href'):  # The item itself is a link
                    links.append(item)

                for link in links:
                    href = link.get('href', '')
                    if 'Record?' in href or '/Home/Record' in href:
                        full_url = urljoin(BASE_URL, href)
                        if full_url not in results:
                            results.append(full_url)
                            print(f"Found record URL: {full_url}")

    # If we still haven't found results, try a more general approach
    if not results:
        print("Trying to find links with 'Record?' in href...")
        all_links = soup.find_all('a', href=True)
        for link in all_links:
            href = link.get('href', '')
            if 'Record?' in href or '/Home/Record' in href:
                full_url = urljoin(BASE_URL, href)
                if full_url not in results:
                    results.append(full_url)
                    print(f"Found record URL: {full_url}")

    print(f"Total records found: {len(results)}")
    return results

def extract_pagination_links(html):
    """Extract pagination links to navigate through all results"""
    soup = BeautifulSoup(html, 'html.parser')
    next_page = None

    # Try different selectors for pagination links
    pagination_selectors = [
        '.pagination a',
        '.pager a',
        'a.next',
        'a.next-page',
        'a[rel="next"]',
        'a:contains("Next")',
        'a:contains("»")'
    ]

    for selector in pagination_selectors:
        try:
            pagination = soup.select(selector)
            if pagination:
                print(f"Found pagination elements using selector: {selector}")
                for link in pagination:
                    link_text = link.text.strip().lower()
                    if "next" in link_text or "»" in link_text or link.get('rel') == ['next']:
                        next_page = urljoin(BASE_URL, link['href'])
                        print(f"Found next page link: {next_page}")
                        return next_page
        except Exception as e:
            print(f"Error with selector {selector}: {e}")

    # Search for script tags that might contain pagination info (for AJAX pagination)
    scripts = soup.find_all('script')
    for script in scripts:
        script_text = script.string if script.string else ""
        if script_text and ("pagination" in script_text.lower() or "pager" in script_text.lower()):
            # Look for patterns like currentPage, nextPage, etc.
            current_page_match = re.search(r'currentPage["\']?\s*[:=]\s*(\d+)', script_text)
            total_pages_match = re.search(r'totalPages["\']?\s*[:=]\s*(\d+)', script_text)

            if current_page_match and total_pages_match:
                current_page = int(current_page_match.group(1))
                total_pages = int(total_pages_match.group(1))

                if current_page < total_pages:
                    # Construct next page URL - this pattern will vary by site
                    next_page = re.sub(r'start=\d+', f'start={current_page * 25}', SEARCH_URL)
                    print(f"Constructed next page URL from script: {next_page}")
                    return next_page

    return next_page

def extract_record_details(html, url):
    """Extract all required fields from a record detail page"""
    soup = BeautifulSoup(html, 'html.parser')
    data = {"url": url}  # Initialize with URL

    # Initialize all fields to None or empty string
    for field in FIELDNAMES:
        if field != "url":  # URL is already added
            data[field] = ""

    # Extract title (name) and remove "Collection search - " prefix
    title_element = soup.find('title')
    if title_element:
        title_text = title_element.text.strip()
        if title_text.startswith("Collection search - "):
            title_text = title_text[len("Collection search - "):]
        data["name"] = title_text

    # Alternative title extraction from h1
    if not data["name"]:
        h1_element = soup.find('h1')
        if h1_element:
            title_text = h1_element.text.strip()
            if title_text.startswith("Collection search - "):
                title_text = title_text[len("Collection search - "):]
            data["name"] = title_text

    # Extract metadata from meta tags
    meta_tags = soup.find_all('meta')
    for meta in meta_tags:
        name = meta.get('name', '')
        content = meta.get('content', '')

        if name.startswith('dcterms.'):
            field_name = name[len('dcterms.'):]
            if field_name == 'title':
                if content.startswith("Collection search - "):
                    content = content[len("Collection search - "):]
                if not data["name"]:
                    data["name"] = content
            elif field_name == 'date' or field_name == 'issued':
                # Extract date from formats like "Date published (2016-11-25) / Date de publication (2016-11-25)"
                date_match = re.search(r'\(([0-9-]+)\)', content)
                if date_match:
                    data["date"] = date_match.group(1)
                else:
                    data["date"] = content
            elif field_name == 'language':
                # Extract language code
                lang_match = re.search(r'content="([a-z]+)"', str(meta))
                if lang_match:
                    data["language of material"] = lang_match.group(1)
                else:
                    data["language of material"] = content
            elif field_name == 'creator':
                data["additional name(s)"] = content
            elif field_name == 'modified':
                # Look for modification date
                mod_match = re.search(r'\(([0-9-]+)\)', content)
                if mod_match and not data["date"]:
                    data["date"] = mod_match.group(1)

    # Extract image URL using the ecopy parameter in the URL
    ecopy_match = re.search(r'ecopy=([^&]+)', url)
    if ecopy_match:
        ecopy = ecopy_match.group(1)
        # Construct image URL based on ecopy number in the requested format
        data["image"] = f"https://central.bac-lac.gc.ca/.item/?id={ecopy}&app=fonandcol&op=img"

    # If no ecopy parameter found, try to find it in the HTML
    if not data["image"]:
        ecopy_match = re.search(r'ecopy=([^&"]+)', html)
        if ecopy_match:
            ecopy = ecopy_match.group(1)
            data["image"] = f"https://central.bac-lac.gc.ca/.item/?id={ecopy}&app=fonandcol&op=img"
        else:
            # Try to find any ecopy reference in the HTML
            ecopy_match = re.search(r'e\d+(-v\d+)?', html)
            if ecopy_match:
                ecopy = ecopy_match.group(0)
                data["image"] = f"https://central.bac-lac.gc.ca/.item/?id={ecopy}&app=fonandcol&op=img"

    # Try to find any tables that might contain metadata
    tables = soup.find_all('table')
    for table in tables:
        rows = table.find_all('tr')
        for row in rows:
            cells = row.find_all(['th', 'td'])
            if len(cells) >= 2:
                label = cells[0].text.strip().lower().rstrip(':')
                value = cells[1].text.strip()

                # Map label to our field names
                if any(keyword in label for keyword in ["title", "name"]) and not data["name"]:
                    data["name"] = value
                elif "place" in label and "creation" not in label:
                    data["place"] = value
                elif any(keyword in label for keyword in ["level", "hierarchical"]):
                    data["hierarchical level"] = value
                elif "date" in label:
                    data["date"] = value
                elif any(keyword in label for keyword in ["item no", "item number", "creator"]):
                    data["item no. (creator)"] = value
                elif any(keyword in label for keyword in ["material type", "type of material"]):
                    data["type of material"] = value
                elif "reference" in label:
                    data["reference"] = value
                elif "found in" in label:
                    data["found in"] = value
                elif any(keyword in label for keyword in ["item id", "id number"]):
                    try:
                        data["item id number"] = int(re.search(r'\d+', value).group())
                    except (AttributeError, ValueError):
                        data["item id number"] = value
                elif "place of creation" in label:
                    data["place of creation"] = value
                elif "extent" in label:
                    data["extent"] = value
                elif any(keyword in label for keyword in ["language", "material language"]):
                    data["language of material"] = value
                elif "scope" in label and "content" in label:
                    data["scope and content"] = value
                elif any(keyword in label for keyword in ["additional name", "other name"]):
                    data["additional name(s)"] = value
                elif "finding aid" in label:
                    data["finding aid no."] = value
                elif "additional information" in label:
                    data["additional information"] = value
                elif "source" in label:
                    data["source"] = value
                elif any(keyword in label for keyword in ["accession", "accession no"]):
                    data["other accession no"] = value
                elif any(keyword in label for keyword in ["subject", "heading"]):
                    data["subject heading"] = value
                elif "access" in label:
                    data["conditions of access"] = value
                elif any(keyword in label for keyword in ["terms of use", "usage", "rights"]):
                    data["terms of use"] = value

    # Look for any divs with class containing 'metadata', 'details', or 'info'
    metadata_divs = soup.find_all('div', class_=lambda c: c and any(term in str(c).lower() for term in ['metadata', 'details', 'info', 'record', 'item']))
    for div in metadata_divs:
        # Look for label-value pairs in various formats
        # Format 1: <div class="label">Label</div><div class="value">Value</div>
        labels = div.find_all(class_=lambda c: c and 'label' in str(c).lower())
        for label_elem in labels:
            value_elem = label_elem.find_next(class_=lambda c: c and 'value' in str(c).lower())
            if value_elem:
                label = label_elem.text.strip().lower().rstrip(':')
                value = value_elem.text.strip()

                # Use the same mapping logic as above
                if any(keyword in label for keyword in ["title", "name"]) and not data["name"]:
                    data["name"] = value
                elif "place" in label and "creation" not in label:
                    data["place"] = value
                # ... and so on for other fields

    # Look for definition lists that might contain metadata
    dl_elements = soup.find_all('dl')
    for dl in dl_elements:
        dt_elements = dl.find_all('dt')
        dd_elements = dl.find_all('dd')

        for i in range(min(len(dt_elements), len(dd_elements))):
            label = dt_elements[i].text.strip().lower().rstrip(':')
            value = dd_elements[i].text.strip()

            # Use the same mapping logic as above
            if any(keyword in label for keyword in ["title", "name"]) and not data["name"]:
                data["name"] = value
            elif "place" in label and "creation" not in label:
                data["place"] = value
            # ... and so on for other fields

    # If we have an item ID in the URL, make sure it's captured
    id_match = re.search(r'IdNumber=(\d+)', url)
    if id_match and not data["item id number"]:
        data["item id number"] = id_match.group(1)

    # Try to extract any text that might contain metadata
    # Look for patterns like "Label: Value" in any text
    for p in soup.find_all(['p', 'div']):
        text = p.text.strip()
        # Look for patterns like "Label: Value"
        matches = re.findall(r'([^:]+):\s*(.+?)(?=\s*[A-Z][^:]+:|$)', text)
        for match in matches:
            if len(match) == 2:
                label = match[0].strip().lower()
                value = match[1].strip()

                # Use the same mapping logic as above
                if any(keyword in label for keyword in ["title", "name"]) and not data["name"]:
                    data["name"] = value
                elif "place" in label and "creation" not in label:
                    data["place"] = value
                elif any(keyword in label for keyword in ["level", "hierarchical"]):
                    data["hierarchical level"] = value
                elif "date" in label:
                    data["date"] = value
                elif any(keyword in label for keyword in ["item no", "item number", "creator"]):
                    data["item no. (creator)"] = value
                elif any(keyword in label for keyword in ["material type", "type of material"]):
                    data["type of material"] = value
                elif "reference" in label:
                    data["reference"] = value
                elif "found in" in label:
                    data["found in"] = value
                # ... and so on for other fields

    # Look for any spans with class containing 'metadata', 'details', or 'info'
    metadata_spans = soup.find_all('span', class_=lambda c: c and any(term in str(c).lower() for term in ['metadata', 'details', 'info', 'field']))
    for span in metadata_spans:
        # Try to find label-value pairs
        text = span.text.strip()
        matches = re.findall(r'([^:]+):\s*(.+)', text)
        for match in matches:
            if len(match) == 2:
                label = match[0].strip().lower()
                value = match[1].strip()

                # Use the same mapping logic as above
                if any(keyword in label for keyword in ["title", "name"]) and not data["name"]:
                    data["name"] = value
                elif "place" in label and "creation" not in label:
                    data["place"] = value
                # ... and so on for other fields

    # Extract any data from the URL parameters
    url_params = url.split('?')[1] if '?' in url else ''
    param_pairs = url_params.split('&')
    for pair in param_pairs:
        if '=' in pair:
            key, value = pair.split('=', 1)
            if key.lower() == 'ecopy' and value:
                data["reference"] = value

    # Look for specific patterns in the HTML that might contain metadata
    # Try to find material type
    if not data["type of material"]:
        material_type_patterns = [
            r'Type of material:\s*([^<]+)',
            r'Material type:\s*([^<]+)',
            r'Document type:\s*([^<]+)'
        ]
        for pattern in material_type_patterns:
            match = re.search(pattern, html, re.IGNORECASE)
            if match:
                data["type of material"] = match.group(1).strip()
                break

    # Try to find date
    if not data["date"]:
        date_patterns = [
            r'Date:\s*([0-9-]+)',
            r'Created:\s*([0-9-]+)',
            r'Publication date:\s*([0-9-]+)'
        ]
        for pattern in date_patterns:
            match = re.search(pattern, html, re.IGNORECASE)
            if match:
                data["date"] = match.group(1).strip()
                break

    # Try to find extent
    if not data["extent"] or 'title=' in data["extent"]:
        extent_match = re.search(r'Extent:?\s*</div>\s*<div[^>]*>\s*([^<]+)', html, re.IGNORECASE)
        if extent_match:
            data["extent"] = extent_match.group(1).strip()
        else:
            # Try alternative patterns
            extent_patterns = [
                r'Extent:\s*([^<]+)',
                r'Physical description:\s*([^<]+)',
                r'Size:\s*([^<]+)'
            ]
            for pattern in extent_patterns:
                match = re.search(pattern, html, re.IGNORECASE)
                if match:
                    data["extent"] = match.group(1).strip()
                    break

        # Clean up any HTML/JavaScript code in the extent field
        if data["extent"] and ('title=' in data["extent"] or 'onclick=' in data["extent"]):
            data["extent"] = ""

    # Try to find place (regular place field, not place of creation)
    if not data["place"]:
        # First try the specific CFCS structure for place
        place_match = re.search(r'<div[^>]*class="[^"]*CFCS-row-label[^"]*"[^>]*>Place:?\s*</div>\s*<div[^>]*class="[^"]*CFCS-row-value[^"]*"[^>]*>\s*([^<]+)', html, re.IGNORECASE | re.DOTALL)
        if place_match:
            data["place"] = place_match.group(1).strip()
        else:
            # Try a more general pattern
            place_match = re.search(r'Place:?\s*</div>\s*<div[^>]*>\s*([^<]+)', html, re.IGNORECASE)
            if place_match:
                data["place"] = place_match.group(1).strip()
            else:
                # Try alternative patterns for place
                place_patterns = [
                    r'Place:\s*([^<]+)',
                    r'Location:\s*([^<]+)',
                    r'Place of publication:\s*([^<]+)'
                ]
                for pattern in place_patterns:
                    match = re.search(pattern, html, re.IGNORECASE)
                    if match:
                        data["place"] = match.group(1).strip()
                        break

    # Try to find place of creation
    if not data["place of creation"]:
        place_match = re.search(r'Place of creation:?\s*</div>\s*<div[^>]*>\s*([^<]+)', html, re.IGNORECASE)
        if place_match:
            data["place of creation"] = place_match.group(1).strip()
        else:
            place_patterns = [
                r'Place of creation:\s*([^<]+)',
                r'Created in:\s*([^<]+)',
                r'Origin:\s*([^<]+)'
            ]
            for pattern in place_patterns:
                match = re.search(pattern, html, re.IGNORECASE)
                if match:
                    data["place of creation"] = match.group(1).strip()
                    break

    # Try to find subject heading
    if not data["subject heading"]:
        # First try to extract from the specific div structure
        subject_div = soup.select_one('div.CFCS-row-label:-soup-contains("Subject heading:") + div.CFCS-row-value')
        if subject_div:
            # Extract all text from the div, including nested elements
            subject_text = subject_div.get_text(separator=' ', strip=True)
            if subject_text:
                data["subject heading"] = subject_text

        # If not found, try regex patterns
        if not data["subject heading"]:
            # Try a specific pattern for subject heading
            subject_match = re.search(r'Subject heading:?\s*</div>\s*<div[^>]*class="[^"]*CFCS-row-value[^"]*"[^>]*>(.*?)</div>', html, re.IGNORECASE | re.DOTALL)
            if subject_match:
                subject_text = subject_match.group(1).strip()
                # Clean up any HTML tags
                subject_text = re.sub(r'<[^>]+>', ' ', subject_text)
                # Clean up whitespace
                subject_text = re.sub(r'\s+', ' ', subject_text).strip()
                data["subject heading"] = subject_text
            else:
                # Try another pattern
                subject_match = re.search(r'recordsubjectfonandcol[^>]*>.*?<span>([^<]+)</span>', html, re.IGNORECASE | re.DOTALL)
                if subject_match:
                    data["subject heading"] = subject_match.group(1).strip()
                else:
                    # Try to find any subject-related text
                    subject_match = re.search(r'Subject:?\s*</div>\s*<div[^>]*>\s*([^<]+)', html, re.IGNORECASE)
                    if subject_match:
                        data["subject heading"] = subject_match.group(1).strip()

    # Try to find conditions of access
    if not data["conditions of access"]:
        # First try to extract from the specific div structure
        access_div = soup.select_one('div.CFCS-row-label:-soup-contains("Conditions of access:") + div.CFCS-row-value')
        if access_div:
            # Extract all CFCS-coa-all divs
            coa_divs = access_div.select('.CFCS-coa-all')
            if coa_divs:
                access_parts = []
                for div in coa_divs:
                    left_div = div.select_one('.CFCS-coa-left')
                    right_div = div.select_one('.CFCS-coa-right')
                    if left_div and right_div:
                        left_text = left_div.get_text(strip=True)
                        right_text = right_div.get_text(strip=True)
                        access_parts.append(f"{left_text} {right_text}")

                if access_parts:
                    data["conditions of access"] = " | ".join(access_parts)

        # If not found, try regex patterns
        if not data["conditions of access"]:
            # Try to extract all CFCS-coa-all divs
            coa_all_matches = re.findall(r'<div[^>]*class="[^"]*CFCS-coa-all[^"]*"[^>]*>.*?<div[^>]*class="[^"]*CFCS-coa-left[^"]*"[^>]*>(.*?)</div>.*?<div[^>]*class="[^"]*CFCS-coa-right[^"]*"[^>]*>(.*?)</div>.*?</div>', html, re.IGNORECASE | re.DOTALL)
            if coa_all_matches:
                access_parts = []
                for left_html, right_html in coa_all_matches:
                    left_text = re.sub(r'<[^>]+>', ' ', left_html).strip()
                    right_text = re.sub(r'<[^>]+>', ' ', right_html).strip()
                    left_text = re.sub(r'\s+', ' ', left_text).strip()
                    right_text = re.sub(r'\s+', ' ', right_text).strip()
                    access_parts.append(f"{left_text} {right_text}")

                if access_parts:
                    data["conditions of access"] = " | ".join(access_parts)
            else:
                # Try a more general pattern
                access_match = re.search(r'Conditions of access:?.*?<div[^>]*class="CFCS-coa-left"[^>]*>.*?<strong><em>([^<]+)</em></strong>.*?</div>', html, re.IGNORECASE | re.DOTALL)
                if access_match:
                    data["conditions of access"] = f"{access_match.group(1).strip()}"
                else:
                    # Try another pattern
                    access_match = re.search(r'recordmediafonandcol[^>]*>.*?<strong><em>([^<]+)</em></strong>', html, re.IGNORECASE | re.DOTALL)
                    if access_match:
                        data["conditions of access"] = f"{access_match.group(1).strip()}"

        # Clean up any JSON data that might have been incorrectly extracted
        if data["conditions of access"] and ('{' in data["conditions of access"] or '}' in data["conditions of access"] or '"text"' in data["conditions of access"]):
            data["conditions of access"] = ""

    # Try to find terms of use
    if not data["terms of use"]:
        # First try to extract from the specific div structure
        terms_div = soup.select_one('div.CFCS-row-label:-soup-contains("Terms of use:") + div.CFCS-row-value')
        if terms_div:
            # Extract all text from the div, including nested elements
            terms_text = terms_div.get_text(separator=' ', strip=True)
            if terms_text:
                data["terms of use"] = terms_text

        # If not found, try regex patterns
        if not data["terms of use"]:
            # Try a specific pattern for terms of use
            terms_match = re.search(r'Terms of use:?\s*</div>\s*<div[^>]*class="[^"]*CFCS-row-value[^"]*"[^>]*>(.*?)</div>', html, re.IGNORECASE | re.DOTALL)
            if terms_match:
                terms_text = terms_match.group(1).strip()
                # Clean up any HTML tags
                terms_text = re.sub(r'<[^>]+>', ' ', terms_text)
                # Clean up whitespace
                terms_text = re.sub(r'\s+', ' ', terms_text).strip()
                data["terms of use"] = terms_text
            else:
                # Try another pattern
                terms_match = re.search(r'recordnotecode111textfonandcol[^>]*>.*?<span>([^<]+)</span>', html, re.IGNORECASE | re.DOTALL)
                if terms_match:
                    data["terms of use"] = terms_match.group(1).strip()
                else:
                    # Try to find any credit or copyright information
                    credit_match = re.search(r'Credit:.*?([^<]+)</div>', html, re.IGNORECASE | re.DOTALL)
                    if credit_match:
                        credit = credit_match.group(1).strip()
                        # Clean up any HTML tags
                        credit = re.sub(r'<[^>]+>', '', credit)
                        if credit and not credit.startswith('/span'):
                            data["terms of use"] = f"Credit: {credit}"

                    copyright_match = re.search(r'Copyright:.*?([^<]+)</div>', html, re.IGNORECASE | re.DOTALL)
                    if copyright_match:
                        copyright = copyright_match.group(1).strip()
                        # Clean up any HTML tags
                        copyright = re.sub(r'<[^>]+>', '', copyright)
                        if copyright and not copyright.startswith('/span'):
                            if data["terms of use"]:
                                data["terms of use"] += f"; Copyright: {copyright}"
                            else:
                                data["terms of use"] = f"Copyright: {copyright}"

        # Clean up any HTML tags in the terms of use field
        if data["terms of use"]:
            data["terms of use"] = re.sub(r'<[^>]+>', '', data["terms of use"])
            # Remove any "/span" text
            data["terms of use"] = re.sub(r'/span', '', data["terms of use"])
            # Clean up any extra spaces or punctuation
            data["terms of use"] = re.sub(r'\s+', ' ', data["terms of use"]).strip()
            data["terms of use"] = re.sub(r';\s*$', '', data["terms of use"]).strip()
            data["terms of use"] = re.sub(r':\s*$', '', data["terms of use"]).strip()
            # If the field is just "Credit:" or "Copyright:" without any value, clear it
            if data["terms of use"] in ["Credit:", "Copyright:", "Credit", "Copyright"]:
                data["terms of use"] = ""

    # Try to find scope and content
    if not data["scope and content"]:
        # First try the specific pattern for scope and content
        scope_match = re.search(r'Scope and content:?\s*</div>\s*<div[^>]*>\s*<div[^>]*>\s*<span>([^<]+)</span>', html, re.IGNORECASE)
        if scope_match:
            data["scope and content"] = scope_match.group(1).strip()
        else:
            # Try a more general pattern
            scope_match = re.search(r'Scope and content:?.*?<span>([^<]+)</span>', html, re.IGNORECASE | re.DOTALL)
            if scope_match:
                data["scope and content"] = scope_match.group(1).strip()
            else:
                # Try another pattern
                scope_match = re.search(r'recordscopefonandcol[^>]*>.*?<span>([^<]+)</span>', html, re.IGNORECASE | re.DOTALL)
                if scope_match:
                    data["scope and content"] = scope_match.group(1).strip()
                else:
                    # Try a simpler pattern
                    scope_match = re.search(r'Scope and content:?\s*</div>\s*<div[^>]*>(.*?)</div>', html, re.IGNORECASE | re.DOTALL)
                    if scope_match:
                        scope_text = scope_match.group(1).strip()
                        # Clean up any HTML tags
                        scope_text = re.sub(r'<[^>]+>', ' ', scope_text)
                        # Clean up whitespace
                        scope_text = re.sub(r'\s+', ' ', scope_text).strip()
                        data["scope and content"] = scope_text

    # Try to find other accession no
    if not data["other accession no"]:
        # First try the specific pattern for other accession no
        acc_no_match = re.search(r'Other accession no\.?:?\s*</div>\s*<div[^>]*>\s*<div[^>]*>\s*<span>([^<]+)</span>', html, re.IGNORECASE)
        if acc_no_match:
            data["other accession no"] = acc_no_match.group(1).strip()
        else:
            # Try a more general pattern
            acc_no_match = re.search(r'Other accession no\.?:?.*?<span>([^<]+)</span>', html, re.IGNORECASE | re.DOTALL)
            if acc_no_match:
                data["other accession no"] = acc_no_match.group(1).strip()
            else:
                # Try another pattern
                acc_no_match = re.search(r'recordcontrolnumbercode154textfonandcol[^>]*>.*?<span>([^<]+)</span>', html, re.IGNORECASE | re.DOTALL)
                if acc_no_match:
                    data["other accession no"] = acc_no_match.group(1).strip()

    # Try to find source
    if not data["source"]:
        source_match = re.search(r'Source:?\s*</div>\s*<div[^>]*>\s*([^<]+)', html, re.IGNORECASE)
        if source_match:
            data["source"] = source_match.group(1).strip()

    # Try to find additional information
    if not data["additional information"]:
        # First try to extract from the specific div structure
        add_info_div = soup.select_one('div.CFCS-row-label:-soup-contains("Additional information:") + div.CFCS-row-value')
        if add_info_div:
            # Extract all text from the div, including nested elements
            add_info_text = add_info_div.get_text(separator=' ', strip=True)
            if add_info_text:
                # Clean up the text
                add_info_text = re.sub(r'Show more Show less', '', add_info_text).strip()
                data["additional information"] = add_info_text

        # If not found, try to find all additional information sections
        if not data["additional information"]:
            additional_info_sections = []

            # Look for Source of title
            source_title_match = re.search(r'Source of title:.*?<span>([^<]+)</span>', html, re.IGNORECASE | re.DOTALL)
            if source_title_match:
                additional_info_sections.append(f"Source of title: {source_title_match.group(1).strip()}")

            # Look for Physical description note
            phys_desc_match = re.search(r'Physical description note:.*?([^<]+)</div>', html, re.IGNORECASE | re.DOTALL)
            if phys_desc_match:
                phys_desc = phys_desc_match.group(1).strip()
                # Clean up any HTML tags
                phys_desc = re.sub(r'<[^>]+>', '', phys_desc)
                additional_info_sections.append(f"Physical description note: {phys_desc}")

            # Look for Notes
            notes_match = re.search(r'Notes:.*?<span>([^<]+)</span>', html, re.IGNORECASE | re.DOTALL)
            if notes_match:
                additional_info_sections.append(f"Notes: {notes_match.group(1).strip()}")

            # Look for General note
            general_note_match = re.search(r'General note:.*?<span>([^<]+)</span>', html, re.IGNORECASE | re.DOTALL)
            if general_note_match:
                additional_info_sections.append(f"General note: {general_note_match.group(1).strip()}")

            # Look for any other notes in the additional information section
            add_info_section_match = re.search(r'Additional information:.*?</div>\s*<div[^>]*>(.*?)</div>\s*<div', html, re.IGNORECASE | re.DOTALL)
            if add_info_section_match:
                section_html = add_info_section_match.group(1)
                # Extract all label-value pairs
                label_value_pairs = re.findall(r'<div[^>]*class="[^"]*row-label[^"]*"[^>]*>(.*?)</div>\s*<div[^>]*class="[^"]*row-value[^"]*"[^>]*>(.*?)</div>', section_html, re.DOTALL)
                for label, value in label_value_pairs:
                    clean_label = re.sub(r'<[^>]+>', '', label).strip().rstrip(':')
                    clean_value = re.sub(r'<[^>]+>', '', value).strip()
                    if clean_label and clean_value and not any(clean_label in section for section in additional_info_sections):
                        additional_info_sections.append(f"{clean_label}: {clean_value}")

            # Combine all sections
            if additional_info_sections:
                data["additional information"] = "; ".join(additional_info_sections)

        # Make sure additional information is not the same as terms of use
        if data["additional information"] and data["terms of use"] and data["additional information"] == data["terms of use"]:
            data["additional information"] = ""

    # Try to find hierarchical level
    if not data["hierarchical level"]:
        level_patterns = [
            r'Hierarchical level:\s*([^<]+)',
            r'Level:\s*([^<]+)',
            r'Record level:\s*([^<]+)'
        ]
        for pattern in level_patterns:
            match = re.search(pattern, html, re.IGNORECASE)
            if match:
                data["hierarchical level"] = match.group(1).strip()
                break

    # If we still don't have a reference, try to extract it from the HTML
    if not data["reference"]:
        # Try to find reference using the div pattern
        ref_match = re.search(r'Reference:?\s*</div>\s*<div[^>]*>\s*([^<]+)', html, re.IGNORECASE)
        if ref_match:
            data["reference"] = ref_match.group(1).strip()
        else:
            # Try alternative patterns
            ref_patterns = [
                r'Reference:\s*([^<]+)',
                r'Reference number:\s*([^<]+)',
                r'Ref:\s*([^<]+)'
            ]
            for pattern in ref_patterns:
                match = re.search(pattern, html, re.IGNORECASE)
                if match:
                    data["reference"] = match.group(1).strip()
                    break

    # Clean up the reference field - remove any ecopy numbers
    if data["reference"] and data["reference"].startswith("e"):
        # If reference is just an ecopy number, try to find a better reference
        ecopy_pattern = r'^e\d+.*$'
        if re.match(ecopy_pattern, data["reference"]):
            # Try to find a better reference
            ref_match = re.search(r'Reference:?\s*</div>\s*<div[^>]*>\s*([^<]+)', html, re.IGNORECASE)
            if ref_match:
                data["reference"] = ref_match.group(1).strip()

    # Final cleanup of all fields
    for key in data:
        if isinstance(data[key], str):
            # Remove leading/trailing whitespace
            data[key] = data[key].strip()

            # Remove any HTML tags
            data[key] = re.sub(r'<[^>]+>', '', data[key])

            # Remove any "/span" text
            data[key] = re.sub(r'/span', '', data[key])

            # Clean up any extra spaces or punctuation
            data[key] = re.sub(r'\s+', ' ', data[key]).strip()

            # Remove any "div id=" text
            data[key] = re.sub(r'div id="[^"]+"', '', data[key])
            data[key] = re.sub(r'class="[^"]+"', '', data[key])

            # Remove "Show more Show less" text
            data[key] = re.sub(r'Show more Show less', '', data[key]).strip()

            # Final cleanup
            data[key] = data[key].strip()

    return data

def scrape_all_results():
    """Main function to scrape all search results"""
    all_record_urls = []
    page_counter = 1
    start_index = 0
    results_per_page = 25

    # Check if the output file already exists and has data
    file_exists = os.path.exists(OUTPUT_FILE)
    last_processed_url = None
    processed_urls = set()

    if file_exists:
        try:
            with open(OUTPUT_FILE, 'r', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                rows = list(reader)
                if rows:
                    # Get all URLs that have already been processed
                    for row in rows:
                        if 'url' in row and row['url']:
                            processed_urls.add(row['url'])

                    # Get the last processed URL
                    if processed_urls:
                        last_processed_url = rows[-1].get('url')
                        print(f"Found existing data with {len(processed_urls)} records")
                        print(f"Last processed URL: {last_processed_url}")

                        # Try to extract the start index from the last URL
                        if last_processed_url:
                            start_match = re.search(r'start=(\d+)', last_processed_url)
                            if start_match:
                                # Calculate the next start index based on the last URL
                                # We'll add results_per_page to ensure we start from the next page
                                last_start = int(start_match.group(1))
                                page_counter = (last_start // results_per_page) + 2  # +2 because we want the next page and pages start at 1
                                start_index = (page_counter - 1) * results_per_page
                                print(f"Continuing from page {page_counter} (start index: {start_index})")
        except Exception as e:
            print(f"Error reading existing file: {e}")
            print("Starting from the beginning")
            file_exists = False

    print("Checking if the site uses AJAX for search results...")

    # If we're resuming from a previous run, skip the AJAX approach
    resuming = file_exists and last_processed_url

    # Create/open CSV file - append if it exists, create new if it doesn't
    mode = 'a' if file_exists else 'w'
    with open(OUTPUT_FILE, mode, newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=FIELDNAMES)
        if not file_exists:
            writer.writeheader()
            csvfile.flush()  # Ensure header is written immediately

        # Try direct AJAX approach first - many modern sites load results via AJAX
        ajax_url_template = "https://recherche-collection-search.bac-lac.gc.ca/eng/Home/AjaxSearchResults?q_type_1=q&q_1=Nunavut&SEARCH_TYPE=SEARCH_BASIC&num={num}&start={start}&enviro=prod&onlinecode=1"

        # Step 1: Try AJAX approach first (unless we're resuming)
        ajax_success = False
        page_record_urls = []

        if resuming:
            print("Resuming from previous run - skipping AJAX approach")
            ajax_success = False  # Skip to HTML approach
        else:
            print("Attempting AJAX-based data extraction...")

        while not resuming:  # Skip the loop if we're resuming
            ajax_url = ajax_url_template.format(num=results_per_page, start=start_index)
            print(f"Requesting AJAX results from index {start_index}...")

            # Try with exponential backoff
            max_retries = 5
            initial_backoff = 5
            backoff = initial_backoff
            success = False

            for retry in range(max_retries):
                try:
                    response = requests.get(ajax_url, headers={
                        'X-Requested-With': 'XMLHttpRequest',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Accept': 'application/json, text/javascript, */*; q=0.01',
                    }, timeout=30)

                    if response.status_code == 200:
                        try:
                            # Try to parse as JSON
                            json_data = response.json()
                            print("Successfully got JSON response!")

                            # Extract record URLs from the JSON response
                            # The structure will depend on the actual API response format
                            if isinstance(json_data, dict) and 'Results' in json_data:
                                records = json_data.get('Results', [])
                                page_record_urls = []  # Reset for this page

                                for record in records:
                                    # Extract URL based on the JSON structure
                                    # This will need adjustment based on actual response format
                                    if isinstance(record, dict) and 'DetailUrl' in record:
                                        url = record['DetailUrl']
                                        full_url = urljoin(BASE_URL, url)
                                        page_record_urls.append(full_url)
                                        print(f"Found record URL via AJAX: {full_url}")
                                    elif isinstance(record, dict) and 'IdNumber' in record:
                                        # Construct URL based on ID if direct URL not provided
                                        id_number = record['IdNumber']
                                        url = f"/eng/Home/Record?app=FonAndCol&IdNumber={id_number}"
                                        full_url = urljoin(BASE_URL, url)
                                        page_record_urls.append(full_url)
                                        print(f"Found record URL via AJAX (constructed): {full_url}")

                                # Process records from this page before moving to the next
                                print(f"Processing {len(page_record_urls)} records from AJAX page {page_counter}...")
                                processed_urls = process_page_records(page_record_urls, writer, csvfile, processed_urls)
                                all_record_urls.extend(page_record_urls)

                                # Check if we should continue to the next page
                                total_results = json_data.get('TotalResults', 0)
                                if start_index + results_per_page < total_results:
                                    start_index += results_per_page
                                    page_counter += 1
                                    ajax_success = True
                                    time.sleep(DELAY)  # Be nice to the server
                                else:
                                    break

                                success = True
                                break  # Break the retry loop on success
                            else:
                                print("JSON response doesn't contain expected 'Results' field")
                                # Dump the JSON structure for debugging
                                print("JSON structure:", json.dumps(json_data)[:500] + "...")
                                if retry < max_retries - 1:
                                    print(f"Retrying in {backoff} seconds...")
                                    time.sleep(backoff)
                                    backoff = min(backoff * 2, 120)  # Cap at 2 minutes
                                else:
                                    break

                        except json.JSONDecodeError:
                            print("Response is not valid JSON")
                            if retry < max_retries - 1:
                                print(f"Retrying in {backoff} seconds...")
                                time.sleep(backoff)
                                backoff = min(backoff * 2, 120)
                            else:
                                print("Falling back to HTML approach")
                                break
                    else:
                        print(f"AJAX request failed with status code: {response.status_code}")
                        if retry < max_retries - 1:
                            print(f"Retrying in {backoff} seconds...")
                            time.sleep(backoff)
                            backoff = min(backoff * 2, 120)
                        else:
                            break

                except requests.exceptions.RequestException as e:
                    print(f"Error with AJAX approach (attempt {retry+1}/{max_retries}): {e}")
                    if retry < max_retries - 1:
                        print(f"Retrying in {backoff} seconds...")
                        time.sleep(backoff)
                        backoff = min(backoff * 2, 120)

                        # If it's a connection reset error, wait a bit longer
                        if "Connection reset by peer" in str(e):
                            print("Connection reset by server. This might be due to rate limiting.")
                            extra_wait = 30
                            print(f"Waiting an additional {extra_wait} seconds...")
                            time.sleep(extra_wait)
                    else:
                        break

            if not success:
                break

        # Step 2: If AJAX approach failed, fall back to traditional HTML scraping
        if not ajax_success:
            print("\nFalling back to traditional HTML scraping...")

            # If we're resuming, try to construct the correct page URL
            if resuming and start_index > 0:
                current_page_url = f"{SEARCH_URL}&start={start_index}"
                page_counter = (start_index // results_per_page) + 1
                print(f"Resuming from page {page_counter} (start index: {start_index})")
            else:
                current_page_url = SEARCH_URL
                page_counter = 1

            while current_page_url:
                print(f"Processing search results page {page_counter}...")
                html = get_page(current_page_url)
                if not html:
                    print(f"Failed to retrieve page {current_page_url}")
                    break

                page_record_urls = extract_search_results(html)

                # Process records from this page before moving to the next
                print(f"Processing {len(page_record_urls)} records from HTML page {page_counter}...")
                processed_urls = process_page_records(page_record_urls, writer, csvfile, processed_urls)

                all_record_urls.extend(page_record_urls)
                print(f"Found {len(page_record_urls)} records on this page. Total so far: {len(all_record_urls)}")

                # Get next page URL
                next_page_url = extract_pagination_links(html)
                if next_page_url and next_page_url != current_page_url:
                    current_page_url = next_page_url
                    page_counter += 1
                    time.sleep(DELAY)  # Be nice to the server
                else:
                    break

    print(f"\nScraping completed! Data saved to {OUTPUT_FILE}")

def process_page_records(record_urls, writer, csvfile, processed_urls=None):
    """Process all records from a single page and write to CSV"""
    if processed_urls is None:
        processed_urls = set()

    for i, record_url in enumerate(record_urls):
        # Skip already processed records
        if record_url in processed_urls:
            print(f"Skipping already processed record {i+1}/{len(record_urls)}: {record_url}")
            continue

        print(f"Processing record {i+1}/{len(record_urls)}: {record_url}")

        # Use the improved get_page function with retry logic
        html = get_page(record_url, max_retries=5, initial_backoff=5)
        if html:
            try:
                record_data = extract_record_details(html, record_url)
                writer.writerow(record_data)
                csvfile.flush()  # Flush to disk after each record
                processed_urls.add(record_url)  # Add to processed set
                print(f"Successfully extracted data for: {record_data.get('name', 'Unknown title')}")
            except Exception as e:
                print(f"Error processing record: {e}")
                print(f"Failed to process record: {record_url}")
        else:
            print(f"Failed to retrieve record at {record_url} after multiple attempts")

        # Add a delay between records to be nice to the server
        # Use a longer delay if we're deep into the results to avoid rate limiting
        if i > 50:
            delay = DELAY * 2
        else:
            delay = DELAY

        print(f"Waiting {delay} seconds before next record...")
        time.sleep(delay)

    return processed_urls

def scrape_directly_by_id():
    """Alternative approach: Scrape records directly by incrementing ID numbers"""
    print("Attempting direct record scraping by ID...")

    # Configure starting parameters
    base_id = 5000000  # Starting ID
    max_attempts = 500  # Maximum number of IDs to try
    consecutive_failures = 0
    max_consecutive_failures = 20  # Stop after this many consecutive failures
    total_scraped = 0

    # Check if the output file already exists and has data
    file_exists = os.path.exists(OUTPUT_FILE)
    processed_ids = set()
    last_id = None

    if file_exists:
        try:
            with open(OUTPUT_FILE, 'r', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                rows = list(reader)
                if rows:
                    # Get all IDs that have already been processed
                    for row in rows:
                        if 'url' in row and row['url']:
                            # Extract ID from URL
                            id_match = re.search(r'IdNumber=(\d+)', row['url'])
                            if id_match:
                                processed_ids.add(int(id_match.group(1)))

                    # Get the last processed ID
                    if processed_ids:
                        last_id = max(processed_ids)
                        print(f"Found existing data with {len(processed_ids)} records")
                        print(f"Last processed ID: {last_id}")

                        # Start from the next ID
                        if last_id:
                            base_id = last_id + 1
                            print(f"Continuing from ID: {base_id}")
        except Exception as e:
            print(f"Error reading existing file: {e}")
            print("Starting from the beginning")
            file_exists = False

    # Create/open CSV file - append if it exists, create new if it doesn't
    mode = 'a' if file_exists else 'w'
    with open(OUTPUT_FILE, mode, newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=FIELDNAMES)
        if not file_exists:
            writer.writeheader()
            csvfile.flush()  # Ensure header is written immediately

        # Try a range of IDs
        for offset in range(max_attempts):
            current_id = base_id + offset
            record_url = f"https://recherche-collection-search.bac-lac.gc.ca/eng/Home/Record?app=FonAndCol&IdNumber={current_id}"

            print(f"Trying ID {current_id}: {record_url}")
            html = get_page(record_url, max_retries=3, initial_backoff=5)

            if html and "Nunavut" in html:
                print(f"Found valid Nunavut record with ID {current_id}")
                try:
                    record_data = extract_record_details(html, record_url)
                    writer.writerow(record_data)
                    csvfile.flush()  # Flush to disk after each record
                    print(f"Successfully extracted data for: {record_data.get('name', 'Unknown title')}")
                    consecutive_failures = 0
                    total_scraped += 1
                except Exception as e:
                    print(f"Error processing record: {e}")
                    consecutive_failures += 1
            else:
                consecutive_failures += 1
                print(f"No valid record found with ID {current_id} (Failures: {consecutive_failures})")

            # Stop if we've had too many consecutive failures
            if consecutive_failures >= max_consecutive_failures:
                print(f"Stopping after {max_consecutive_failures} consecutive failures")
                break

            time.sleep(DELAY)  # Be nice to the server

    print(f"\nDirect ID scraping completed! Scraped {total_scraped} records. Data saved to {OUTPUT_FILE}")

if __name__ == "__main__":
    print("Starting Library and Archives Canada Web Scraper...")

    # Try the standard search results approach first
    try:
        scrape_all_results()
    except Exception as e:
        print(f"Error with search results approach: {e}")

    # If we didn't find any records, try the direct ID approach
    with open(OUTPUT_FILE, 'r', encoding='utf-8') as f:
        if len(f.readlines()) <= 1:  # Only header exists
            print("\nNo records found using search results approach, trying direct ID scraping...")
            scrape_directly_by_id()
