import boto3
import os
from dotenv import load_dotenv
import argparse
import io
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter

# Load environment variables
load_dotenv()

def create_sample_pdf(filename="sample_document.pdf", content=None):
    """
    Create a sample PDF file in memory
    
    Args:
        filename: Name of the PDF file
        content: Content to include in the PDF
    
    Returns:
        BytesIO object containing the PDF
    """
    buffer = io.BytesIO()
    c = canvas.Canvas(buffer, pagesize=letter)
    
    # Default content if none provided
    if content is None:
        content = [
            "Sample Sales Document",
            "",
            "This is a sample sales document for the Custom Sales consultant.",
            "",
            "Sales Strategy:",
            "1. Identify target customers",
            "2. Understand customer needs",
            "3. Present solutions that address those needs",
            "4. <PERSON><PERSON> objections effectively",
            "5. Close the sale",
            "",
            "Key Sales Metrics:",
            "- Conversion rate: The percentage of leads that become customers",
            "- Average deal size: The average value of each sale",
            "- Sales cycle length: The average time it takes to close a sale",
            "- Customer acquisition cost: The cost of acquiring a new customer",
            "",
            "Best Practices:",
            "- Always follow up with prospects",
            "- Listen more than you talk",
            "- Focus on benefits, not features",
            "- Build relationships, not just transactions",
            "- Continuously improve your sales skills"
        ]
    
    # Write content to PDF
    text_object = c.beginText(40, 750)
    for line in content:
        text_object.textLine(line)
    c.drawText(text_object)
    
    c.showPage()
    c.save()
    
    buffer.seek(0)
    return buffer

def upload_to_s3(s3_path, filename=None, content=None):
    """
    Upload a sample PDF to S3
    
    Args:
        s3_path: The S3 path to upload to (e.g., 'baas/automateintelpro/companies/34/group/114/')
        filename: Name of the PDF file
        content: Content to include in the PDF
    """
    # Get AWS credentials from environment variables
    aws_access_key = os.getenv('AWS_ACCESS_KEY')
    aws_secret = os.getenv('AWS_SECRET')
    bucket = os.getenv('AWS_S3_BUCKET')
    
    if not all([aws_access_key, aws_secret, bucket]):
        print("Error: AWS credentials or bucket name not found in environment variables.")
        print("Make sure AWS_ACCESS_KEY, AWS_SECRET, and AWS_S3_BUCKET are set.")
        return
    
    # Create S3 client
    s3_client = boto3.client(
        's3',
        aws_access_key_id=aws_access_key,
        aws_secret_access_key=aws_secret
    )
    
    # Default filename if none provided
    if filename is None:
        filename = "sample_document.pdf"
    
    # Create full S3 key (path + filename)
    s3_key = os.path.join(s3_path, filename)
    if s3_key.startswith('/'):
        s3_key = s3_key[1:]
    
    print(f"Creating sample PDF: {filename}")
    pdf_buffer = create_sample_pdf(filename, content)
    
    print(f"Uploading to S3: s3://{bucket}/{s3_key}")
    try:
        s3_client.upload_fileobj(
            pdf_buffer,
            bucket,
            s3_key,
            ExtraArgs={'ContentType': 'application/pdf'}
        )
        print(f"Successfully uploaded {filename} to s3://{bucket}/{s3_key}")
        return True
    except Exception as e:
        print(f"Error uploading to S3: {str(e)}")
        return False

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Upload a sample PDF to S3")
    parser.add_argument("--path", default="baas/automateintelpro/companies/34/group/114/", 
                        help="S3 path to upload to")
    parser.add_argument("--filename", default="sample_sales_document.pdf", 
                        help="Name of the PDF file")
    
    args = parser.parse_args()
    
    upload_to_s3(args.path, args.filename)
