#!/usr/bin/env python3
import requests
import re
from bs4 import BeautifulSoup

def analyze_specific_fields(url):
    """Analyze a record page to find specific metadata fields"""
    response = requests.get(url)
    html = response.text
    soup = BeautifulSoup(html, 'html.parser')
    
    print(f"Analyzing URL: {url}")
    print("=" * 80)
    
    # Look for scope and content
    print("\nLooking for scope and content:")
    scope_content_patterns = [
        r'Scope and content:?\s*</div>\s*<div[^>]*>(.*?)</div>',
        r'Scope and content:.*?<span>(.*?)</span>',
        r'recordscopefonandcol[^>]*>.*?<span>(.*?)</span>'
    ]
    
    for pattern in scope_content_patterns:
        matches = re.findall(pattern, html, re.IGNORECASE | re.DOTALL)
        if matches:
            for match in matches:
                clean_match = re.sub(r'<[^>]+>', '', match).strip()
                print(f"  Pattern '{pattern}' found: {clean_match[:200]}...")
    
    # Look for subject heading
    print("\nLooking for subject heading:")
    subject_patterns = [
        r'Subject heading:?\s*</div>\s*<div[^>]*>(.*?)</div>',
        r'Subject heading:.*?<span>(.*?)</span>',
        r'recordsubjectfonandcol[^>]*>.*?<span>(.*?)</span>',
        r'Subject:?\s*</div>\s*<div[^>]*>(.*?)</div>'
    ]
    
    for pattern in subject_patterns:
        matches = re.findall(pattern, html, re.IGNORECASE | re.DOTALL)
        if matches:
            for match in matches:
                clean_match = re.sub(r'<[^>]+>', '', match).strip()
                print(f"  Pattern '{pattern}' found: {clean_match[:200]}...")
    
    # Look for terms of use
    print("\nLooking for terms of use:")
    terms_patterns = [
        r'Terms of use:?\s*</div>\s*<div[^>]*>(.*?)</div>',
        r'Terms of use:.*?<span>(.*?)</span>',
        r'recordnotecode111textfonandcol[^>]*>.*?<span>(.*?)</span>',
        r'Credit:.*?([^<]+)</div>',
        r'Copyright:.*?([^<]+)</div>'
    ]
    
    for pattern in terms_patterns:
        matches = re.findall(pattern, html, re.IGNORECASE | re.DOTALL)
        if matches:
            for match in matches:
                clean_match = re.sub(r'<[^>]+>', '', match).strip()
                print(f"  Pattern '{pattern}' found: {clean_match[:200]}...")
    
    # Look for conditions of access
    print("\nLooking for conditions of access:")
    access_patterns = [
        r'Conditions of access:?\s*</div>\s*<div[^>]*>(.*?)</div>',
        r'Conditions of access:.*?<div[^>]*class="CFCS-coa-all"[^>]*>(.*?)</div>',
        r'recordmediafonandcol[^>]*>(.*?)</div>',
        r'<div[^>]*class="CFCS-coa-all"[^>]*>(.*?)</div>'
    ]
    
    for pattern in access_patterns:
        matches = re.findall(pattern, html, re.IGNORECASE | re.DOTALL)
        if matches:
            for match in matches:
                clean_match = re.sub(r'<[^>]+>', '', match).strip()
                print(f"  Pattern '{pattern}' found: {clean_match[:200]}...")
    
    # Look for all CFCS-row-label and CFCS-row-value pairs
    print("\nLooking for all CFCS-row-label and CFCS-row-value pairs:")
    label_value_pairs = re.findall(r'<div[^>]*class="[^"]*CFCS-row-label[^"]*"[^>]*>(.*?)</div>\s*<div[^>]*class="[^"]*CFCS-row-value[^"]*"[^>]*>(.*?)</div>', html, re.DOTALL)
    for i, (label, value) in enumerate(label_value_pairs[:10]):  # Show first 10 pairs only
        clean_label = re.sub(r'<[^>]+>', '', label).strip()
        clean_value = re.sub(r'<[^>]+>', '', value).strip()
        print(f"  Pair {i+1}: {clean_label} = {clean_value[:100]}...")
    
    # Look for all CFCS-coa-all divs
    print("\nLooking for all CFCS-coa-all divs:")
    coa_divs = soup.select('.CFCS-coa-all')
    for i, div in enumerate(coa_divs[:5]):  # Show first 5 divs only
        left_div = div.select_one('.CFCS-coa-left')
        right_div = div.select_one('.CFCS-coa-right')
        left_text = left_div.text.strip() if left_div else ""
        right_text = right_div.text.strip() if right_div else ""
        print(f"  Div {i+1}: Left = {left_text}, Right = {right_text}")

if __name__ == "__main__":
    # Analyze a sample record
    urls = [
        "https://recherche-collection-search.bac-lac.gc.ca/eng/Home/Record?app=fonandcol&IdNumber=4942854&q_type_1=q&q_1=Nunavut&ecopy=e010864242-v8",
        "https://recherche-collection-search.bac-lac.gc.ca/eng/Home/Record?app=fonandcol&IdNumber=5080854&q_type_1=q&q_1=Nunavut&ecopy=e010836252-v8",
        "https://recherche-collection-search.bac-lac.gc.ca/eng/Home/Record?app=fonandcol&IdNumber=3612843&q_type_1=q&q_1=Nunavut&ecopy=e003525197-v6"
    ]
    
    for url in urls:
        analyze_specific_fields(url)
        print("\n" + "=" * 80 + "\n")
