 

<!DOCTYPE html>
<!--[if lt IE 9]><html class="no-js lt-ie9" lang="en" dir="ltr"><![endif]--><!--[if gt IE 8]><!-->
<html class="no-js" lang="en" dir="ltr">
<!--<![endif]-->
<head>
    <meta charset="utf-8">
    <!-- Web Experience Toolkit (WET) / Boîte à outils de l'expérience Web (BOEW) wet-boew.github.io/wet-boew/License-en.html / wet-boew.github.io/wet-boew/Licence-fr.html -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>Collection search - [Osuitok Ipeelee, Kinngait, Nunavut]</title>
    <meta content="width=device-width,initial-scale=1" name="viewport">
    <!-- Meta data -->
    <meta name="dcterms.title" content="Collection search - [Osuitok Ipeelee, Kinngait, Nunavut]">
    <meta name="dcterms.creator" content="Library and Archives Canada">
    <meta name="dcterms.language" title="ISO639-2" content="eng">
    <meta name="dcterms.service" content="BAC-LAC">
    <meta name="dcterms.accessRights" content="2">
    <meta name="dcterms.modified" title="W3CDTF" content="2025-05-06"/>
    
    <meta name="description" content="Application Description">
    <meta name="dcterms.issued" title="W3CDTF" content="Date published (2016-11-25) / Date de publication (2016-11-25)">
    <meta name="dcterms.modified" title="W3CDTF" content="Date modified (2023-12-12) / Date de modification (2023-12-12)">
    <meta name="dcterms.subject" title="scheme" content="French subject terms / Termes de sujet en français">
    
 
 
    <script src="https://www.canada.ca/etc/designs/canada/cdts/gcweb/v5_0_2/cdts/compiled/wet-en.js"></script>
    <!--[if gte IE 9 | !IE ]><!-->
    <link rel="apple-touch-icon" sizes="57x57 72x72 114x114 144x144 150x150" class="wb-favicon" href="https://www.canada.ca/etc/designs/canada/cdts/gcweb/v5_0_2/wet-boew/assets/favicon-mobile.png">
    <link href="https://www.canada.ca/etc/designs/canada/cdts/gcweb/v5_0_2/wet-boew/assets/favicon.ico" rel="icon" type="image/x-icon" class="wb-init wb-favicon-inited">
    
    
        <link rel="stylesheet" href="https://www.canada.ca/etc/designs/canada/cdts/gcweb/v5_0_2/wet-boew/css/theme.min.css" />
    
    <link rel="stylesheet" href="https://www.canada.ca/etc/designs/canada/cdts/gcweb/v5_0_2/cdts/cdtsfixes.css" />
    <link rel="stylesheet" href="https://www.canada.ca/etc/designs/canada/cdts/gcweb/v5_0_2/cdts/cdtsapps.css" />
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.1/css/all.css" integrity="sha384-50oBUHEmvpQ+1lW4y57PTFmhCaXp0ML5d60M1M7uH2+nqUivzIebhndOJK28anvf" crossorigin="anonymous" />

    <script src="https://www.canada.ca/etc/designs/canada/cdts/gcweb/v5_0_2/wet-boew/js/jquery/2.2.4/jquery.min.js"></script>
<script>(window.jQuery||document.write("\u003Cscript src=\u0022/cdts/gcweb/v5_0_2/wet-boew/js/jquery/2.2.4/jquery.min.js\u0022 src=\u0022/cdts/gcweb/v5_0_2/wet-boew/js/jquery/2.2.4/jquery.min.js\u0022\u003E\u003C/script\u003E"));</script>
    <!--<![endif]-->
    <!--[if lt IE 9]>
    <link href="https://www.canada.ca/etc/designs/canada/cdts/gcweb/v5_0_2/wet-boew/assets/favicon.ico" rel="shortcut icon">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" href="/GcWeb/css/site.min.css" />

    
    <link href="/lib/jquery-ui-1.13.2/jquery-ui.min.css" rel="stylesheet" />
        <link rel="stylesheet" href="/css/site.css?v=LaWiDR1Tm4z_jRmuHihM9L9cYgA">
        <link rel="stylesheet" href="https://colabapi.bac-lac.gc.ca//hv/uv/uv.css" />
        <style>
            select.form-control {
            -webkit-appearance: menulist !important;
            -moz-appearance: menulist !important;
            -ms-appearance: menulist !important;
            -o-appearance: menulist !important;
            appearance: menulist !important;
            }
        </style>
        
    <link href="/css/RecordDisplay.css?v=uf8fPCTxFIlXGjHptvQ1Tp3aiRc" rel="stylesheet" />
    <link href="/css/HVContainer.css?v=hY2cVgYIz1BU4otJmNCH48Yjlts" rel="stylesheet" />



        <script src="//assets.adobedtm.com/be5dfd287373/0127575cd23a/launch-f7c3e6060667.min.js"></script>

</head>
<body class="page-type-nav" vocab="http://schema.org/" typeof="WebPage">
    <div id="def-top">
    </div>
    <!-- Write closure template -->
    <script>
        var defTop = document.getElementById("def-top");
        defTop.outerHTML = wet.builder.appTop({
  "appName": [
    {
      "text": "Collection search",
      "href": "/eng/home/<USER>"
    }
  ],
  "breadcrumbs": [
    {
      "title": "Canada.ca",
      "href": "https://www.canada.ca/en.html"
    },
    {
      "title": "Library and Archives",
      "href": "https://library-archives.canada.ca/eng"
    }
  ],
  "lngLinks": [
    {
      "lang": "fr",
      "href": "/fra/accueil/notice?app=fonandcol\u0026IdNumber=5080778\u0026ecopy=e010836176-v8",
      "text": "Fran\u00E7ais"
    }
  ],
  "signIn": [
    {
      "href": "/eng/account/login"
    }
  ],
  "menuLinks": [],
  "search": false,
  "siteMenu": false,
  "showPreContent": false
});
    </script>

    <main role="main" property="mainContentOfPage" class="container">

        

        
    


        <h1 property="name" id="wb-cont">Collection search - [Osuitok Ipeelee, Kinngait, Nunavut]</h1>

        




    <script>
        $('a.app-name').get(0).innerHTML = 'Collection search';
    </script>
    



            <script nomodule src="https://colabapi.bac-lac.gc.ca/hv/colab/lac-harmonized-viewer-module/lac-harmonized-viewer-module.js"></script>
            <script type="module" src="https://colabapi.bac-lac.gc.ca/hv/colab/lac-harmonized-viewer-module/lac-harmonized-viewer-module.esm.js"></script>
            <script src="https://colabapi.bac-lac.gc.ca//hv/uv/umd/UV.js"></script>
            <script>var uvj$ = jQuery.noConflict()</script>
            <div>
                <lac-harmonized-viewer id="hv-fonandcol5080778"
                                       reference-system="fonandcol"
                                       item-number="5080778"
                                       app-Environment="Prod"
                                       language="en"
                                       ecopy="e010836176-v8"
                                       style="display:block"
                                       suppress-gallery="false"
                                       kwic-q=""
                                       kwic-q-exact=""
                                       kwic-q-any=""
                                       kwic-q-none=""
                                       kwic-count=""
                                       kwic-pages=""
                                       kwic-ecopies="">
                </lac-harmonized-viewer>
            </div>
            <div id="record-display">
                <ul class="list-unstyled">
                        

    <li>
        <section>
            <a href="javascript:;" id="jq-hierarchy-a" class="CFCS-toggle CFCS-toggle-opened" aria-expanded="true" data-target="#jq-hierarchy" aria-controls="jq-hierarchy">
                <i class="fas fa-angle-down CFCS-icon-down"></i>
                <i class="fas fa-angle-right CFCS-icon-right"></i>
                Hierarchy
            </a>
            <div id="jq-hierarchy" class="CFCS-collapsible" role="region" tabindex="-1">
                <div id="jq-record-fonandcol5080778" class="CFCS-table-flex">
                    <div class="CFCS-record-ck-div CFCS-hide-to-printer CFCS-table-cell-flex CFCS-hide">1</div>
                    <div class="CFCS-table-cell-flex CFCS-record-thumb-wrapper CFCS-hide"><img src="/images/generic.jpg" style="width: 132px" /></div>
                    <div class="CFCS-table-cell-flex">
                        <a href="https://recherche-collection-search.bac-lac.gc.ca/eng/Home/Record?app=fonandcol&amp;IdNumber=5080778&amp;ecopy=e010836176-v8" target="_blank" class="CFCS-hide"><h3 property="name">[Osuitok Ipeelee, Kinngait, Nunavut]</h3></a>
                        <div>
                            <div id="dl-hierarchy-fonandcol5080778" class="CFCS-margin-b-0 CFCS-table-flex">
                                    

    <div class="CFCS-table-row-flex">
        <div id="dt-recordhierarchylevelenfonandcol5080778" class="CFCS-row-label ">Hierarchical level:</div>
        <div id="dd-recordhierarchylevelenfonandcol5080778" class="CFCS-row-value ">
Item        </div>
    </div>

                                        

    <div class="CFCS-table-row-flex">
        <div id="dt-recordhierarchycontextfonandcol5080778" class="CFCS-row-label ">Context of this record:</div>
        <div id="dd-recordhierarchycontextfonandcol5080778" class="CFCS-row-value ">
                

    <div id="jq-container-body-recordhierarchycontextfonandcol5080778" class="CFCS-field-container" style="">
            
    <div id="jq-context-hierarchycontext-fonandcol5080778" class="CFCS-hierarchy-context CFCS-float-left-clear" style="">
        <ul id="jq-context-ul-hierarchycontext-fonandcol5080778">
                <li class="CFCS-table-indent-0">
                    <div class="CFCS-display-table-row">
                        <div id="folder-icon-0" class="CFCS-bg-img-dot-vertical CFCS-display-table-cell">
                            <a href="http://central.bac-lac.gc.ca:80/.redirect?app=FonAndCol&amp;id=3803244&amp;lang=eng" title="Rosemary Gilliat Eaton fonds">
                                <img src="/images/folder-10.png" height="20" width="20" alt="Rosemary Gilliat Eaton fonds" />
                            </a>
                        </div>
                        <div id="title-txt-0" class="CFCS-display-table-cell CFCS-padding-l-5">
                            <a href="http://central.bac-lac.gc.ca:80/.redirect?app=FonAndCol&amp;id=3803244&amp;lang=eng" title="Rosemary Gilliat Eaton fonds">Rosemary Gilliat Eaton fonds</a>
                        </div>
                    </div>
                </li>
                <li class="CFCS-table-indent-1">
                    <div class="CFCS-display-table-row">
                            <div id="branch-icon-1" class="CFCS-display-table-cell CFCS-width-20px CFCS-bg-img-empty-vertical"><img src="/images/joinbottom.gif" width="19" /></div>
                        <div id="folder-icon-1" class="CFCS-bg-img-dot-vertical CFCS-display-table-cell">
                            <a href="http://central.bac-lac.gc.ca:80/.redirect?app=FonAndCol&amp;id=4119858&amp;lang=eng" title="Arctic travel series">
                                <img src="/images/folder-30.png" height="20" width="20" alt="Arctic travel series" />
                            </a>
                        </div>
                        <div id="title-txt-1" class="CFCS-display-table-cell CFCS-padding-l-5">
                            <a href="http://central.bac-lac.gc.ca:80/.redirect?app=FonAndCol&amp;id=4119858&amp;lang=eng" title="Arctic travel series">Arctic travel series</a>
                        </div>
                    </div>
                </li>
                <li class="CFCS-table-indent-2">
                    <div class="CFCS-display-table-row">
                            <div id="branch-icon-2" class="CFCS-display-table-cell CFCS-width-20px CFCS-bg-img-empty-vertical"><img src="/images/joinbottom.gif" width="19" /></div>
                        <div id="folder-icon-2" class="CFCS-bg-img-empty-vertical CFCS-display-table-cell">
                            <a href="http://central.bac-lac.gc.ca:80/.redirect?app=FonAndCol&amp;id=5080778&amp;lang=eng" title="[Osuitok Ipeelee, Kinngait, Nunavut]">
                                <img src="/images/folder-80.png" height="20" width="20" alt="[Osuitok Ipeelee, Kinngait, Nunavut]" />
                            </a>
                        </div>
                        <div id="title-txt-2" class="CFCS-display-table-cell CFCS-padding-l-5">
                            <a href="http://central.bac-lac.gc.ca:80/.redirect?app=FonAndCol&amp;id=5080778&amp;lang=eng" title="[Osuitok Ipeelee, Kinngait, Nunavut]">[Osuitok Ipeelee, Kinngait, Nunavut]</a>
                        </div>
                    </div>
                </li>
        </ul>
    </div>

    </div>
    <div id="jq-container-toggle-recordhierarchycontextfonandcol5080778" style="display:none" aria-hidden="true">
        <button id="jq-show-link-btn-recordhierarchycontextfonandcol5080778" class="CFCS-text-underline btn btn-link"
           onclick="$(this).hide(); $('#jq-hide-link-btn-recordhierarchycontextfonandcol5080778').show(); $('#jq-container-body-recordhierarchycontextfonandcol5080778').removeClass('CFCS-Text-truncated-2l', 200);"
           style="text-decoration:none; padding: 0;">Show more</button>
        <button id="jq-hide-link-btn-recordhierarchycontextfonandcol5080778" class="CFCS-text-underline btn btn-link"
           onclick="$(this).hide(); $('#jq-show-link-btn-recordhierarchycontextfonandcol5080778').show(); $('#jq-container-body-recordhierarchycontextfonandcol5080778').addClass('CFCS-Text-truncated-2l', 200);"
           style="text-decoration:none; padding: 0;">Show less</button>
    </div>

        </div>
    </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </li>

                        

        <li>
            <section>
                <a href="javascript:;" id="jq-findingaid-a" class="CFCS-toggle CFCS-toggle-opened" aria-expanded="true" data-target="#jq-findingaid" aria-controls="jq-findingaid">
                    <i class="fas fa-angle-down CFCS-icon-down"></i>
                    <i class="fas fa-angle-right CFCS-icon-right"></i>
                    Finding aid
                </a>
                <div id="jq-findingaid" class="CFCS-collapsible" role="region" tabindex="-1">
                    <div id="dl-findingaid-fonandcol5080778" class="CFCS-margin-b-0 CFCS-table-flex">
                            <div id="dl-findingaid-nbr-fonandcol5080778" class="CFCS-field-container">
See other records related to finding aid                                    

        <a id="link-recordcontrolnumbercode162andtextfonandcol5080778" name="link-recordcontrolnumbercode162andtextfonandcol5080778" href="https://recherche-collection-search.bac-lac.gc.ca/eng/Home/Result?DataSource=Archives|FonAndCol&amp;SearchIn_1=FindingAidNumber&amp;SearchInText_1=FA-457&amp;SEARCH_TYPE=SEARCH_ADVANCED">FA-457</a>

. Some archival records may not be described in Collection search.                            </div>
                    </div>
                </div>
            </section>
        </li>

                    

    <li>
        <section>
            <a href="javascript:;" id="jq-brief-a" class="CFCS-toggle CFCS-toggle-opened" aria-expanded="true" data-target="#jq-brief" aria-controls="jq-brief">
                <i class="fas fa-angle-down CFCS-icon-down"></i>
                <i class="fas fa-angle-right CFCS-icon-right"></i>
                Record information
            </a>
            <div id="jq-brief" class="CFCS-collapsible" role="region" tabindex="-1">
                <div id="jq-record-fonandcol5080778" class="CFCS-table-flex">
                    <div class="CFCS-record-ck-div CFCS-hide-to-printer CFCS-table-cell-flex CFCS-hide">1</div>
                    <div class="CFCS-table-cell-flex CFCS-record-thumb-wrapper CFCS-hide"><img src="/images/generic.jpg" style="width: 132px" /></div>
                    <div class="CFCS-table-cell-flex">
                        <a href="https://recherche-collection-search.bac-lac.gc.ca/eng/Home/Record?app=fonandcol&amp;IdNumber=5080778&amp;ecopy=e010836176-v8" target="_blank" class="CFCS-hide"><h3 property="name">[Osuitok Ipeelee, Kinngait, Nunavut]</h3></a>
                        <div>
                                

    <div id="dl-brief-fonandcol5080778" class="CFCS-margin-b-0 CFCS-table-flex">
            

    <div class="CFCS-table-row-flex">
        <div id="dt-recordnotecode24datesofcreationfonandcol5080778" class="CFCS-row-label ">Date:</div>
        <div id="dd-recordnotecode24datesofcreationfonandcol5080778" class="CFCS-row-value ">
[between 1956-1960]        </div>
    </div>

            

    <div class="CFCS-table-row-flex">
        <div id="dt-recordreferenceenfonandcol5080778" class="CFCS-row-label ">Reference:</div>
        <div id="dd-recordreferenceenfonandcol5080778" class="CFCS-row-value ">
R12438-3544-0-E, Volume number: 9        </div>
    </div>

            


            


            


            


            


            

    <div class="CFCS-table-row-flex">
        <div id="dt-recordtypeofmaterialenfonandcol5080778" class="CFCS-row-label ">Type of material:</div>
        <div id="dd-recordtypeofmaterialenfonandcol5080778" class="CFCS-row-value ">
Photographs        </div>
    </div>

            

    

    <div class="CFCS-table-row-flex">
        <div id="dt-foundinfonandcol5080778" class="CFCS-row-label ">Found in:</div>
        <div id="dd-foundinfonandcol5080778" class="CFCS-row-value ">
 Archives / Collections and Fonds        </div>
    </div>

   
            

        
            

    

    <div class="CFCS-table-row-flex">
        <div id="dt-idnumberfonandcol5080778" class="CFCS-row-label ">Item ID number:</div>
        <div id="dd-idnumberfonandcol5080778" class="CFCS-row-value ">
5080778        </div>
    </div>


            

    <div class="CFCS-table-row-flex">
        <div id="dt-recordnotecode24datesofcreationfonandcol5080778" class="CFCS-row-label ">Date(s):</div>
        <div id="dd-recordnotecode24datesofcreationfonandcol5080778" class="CFCS-row-value ">
[between 1956-1960]        </div>
    </div>

            

    <div class="CFCS-table-row-flex">
        <div id="dt-recordnotecode24placeofpublicationfonandcol5080778" class="CFCS-row-label ">Place:</div>
        <div id="dd-recordnotecode24placeofpublicationfonandcol5080778" class="CFCS-row-value ">
Cape Dorset (Kinngait), Nunavut:        </div>
    </div>

            

    <div class="CFCS-table-row-flex">
        <div id="dt-recordplaceofcreationenfonandcol5080778" class="CFCS-row-label ">Place of creation:</div>
        <div id="dd-recordplaceofcreationenfonandcol5080778" class="CFCS-row-value ">
Nunavut        </div>
    </div>

            


            


            


            


            

    <div class="CFCS-table-row-flex">
        <div id="dt-phydesextentfonandcol5080778" class="CFCS-row-label ">Extent:</div>
        <div id="dd-phydesextentfonandcol5080778" class="CFCS-row-value ">
1 photograph : b&w negative.        </div>
    </div>

            

    <div class="CFCS-table-row-flex">
        <div id="dt-recordlanguageofmaterialenfonandcol5080778" class="CFCS-row-label ">Language of material:</div>
        <div id="dd-recordlanguageofmaterialenfonandcol5080778" class="CFCS-row-value ">
English        </div>
    </div>

            


            


            

    <div class="CFCS-table-row-flex">
        <div id="dt-recordnotecode205textfonandcol5080778" class="CFCS-row-label ">Scope and content:</div>
        <div id="dd-recordnotecode205textfonandcol5080778" class="CFCS-row-value ">
                

    <div id="jq-container-body-recordnotecode205textfonandcol5080778" class="CFCS-field-container" style="">
            <span>Photograph depicting artist and sculptor Osuitok Ipeelee. Osuitok Ipeelee is Inuk. The photograph was taken in Cape Dorset (Kinngait), an Inuit community.</span>
    </div>
    <div id="jq-container-toggle-recordnotecode205textfonandcol5080778" style="display:none" aria-hidden="true">
        <button id="jq-show-link-btn-recordnotecode205textfonandcol5080778" class="CFCS-text-underline btn btn-link"
           onclick="$(this).hide(); $('#jq-hide-link-btn-recordnotecode205textfonandcol5080778').show(); $('#jq-container-body-recordnotecode205textfonandcol5080778').removeClass('CFCS-Text-truncated-2l', 200);"
           style="text-decoration:none; padding: 0;">Show more</button>
        <button id="jq-hide-link-btn-recordnotecode205textfonandcol5080778" class="CFCS-text-underline btn btn-link"
           onclick="$(this).hide(); $('#jq-show-link-btn-recordnotecode205textfonandcol5080778').show(); $('#jq-container-body-recordnotecode205textfonandcol5080778').addClass('CFCS-Text-truncated-2l', 200);"
           style="text-decoration:none; padding: 0;">Show less</button>
    </div>
        <span id="jq-container-script-recordnotecode205textfonandcol5080778">
            <script>
let textLengthrecordnotecode205textfonandcol5080778 = $('#jq-container-body-recordnotecode205textfonandcol5080778').text().length;
let containerHeightrecordnotecode205textfonandcol5080778 = $('#jq-container-body-recordnotecode205textfonandcol5080778').outerHeight();
if (containerHeightrecordnotecode205textfonandcol5080778 > 90) {
  $('#jq-container-toggle-recordnotecode205textfonandcol5080778').show();
  $('#jq-show-link-btn-recordnotecode205textfonandcol5080778').show();
  $('#jq-hide-link-btn-recordnotecode205textfonandcol5080778').hide();
  $('#jq-container-body-recordnotecode205textfonandcol5080778').addClass('CFCS-Text-truncated-2l');
} else {
  $('#jq-container-toggle-recordnotecode205textfonandcol5080778').hide();
  $('#jq-show-link-btn-recordnotecode205textfonandcol5080778').hide();
  $('#jq-hide-link-btn-recordnotecode205textfonandcol5080778').hide();
  $('#jq-container-body-recordnotecode205textfonandcol5080778').removeClass('CFCS-Text-truncated-2l');
}
</script>
        </span>

        </div>
    </div>

            


            

    <div class="CFCS-table-row-flex">
        <div id="dt-recordadditionalnamefonandcol5080778" class="CFCS-row-label ">Additional name(s):</div>
        <div id="dd-recordadditionalnamefonandcol5080778" class="CFCS-row-value ">
                

    <div id="jq-container-body-recordadditionalnamefonandcol5080778" class="CFCS-field-container" style="">
            

        <ol id="ol-recordadditionalnamefonandcol5080778" class="">
                <li id="li-recordadditionalnamefonandcol5080778">
                    <a id="link-recordadditionalnamefonandcol5080778" name="link-recordadditionalnamefonandcol5080778" href="https://recherche-collection-search.bac-lac.gc.ca/eng/Home/Result?DataSource=Archives|FonAndCol&amp;SearchIn_1=Name&amp;SearchInText_1=Ipeelee, Osuitok, 1922-2005&amp;SEARCH_TYPE=SEARCH_ADVANCED">Depicted: Ipeelee, Osuitok, 1922-2005</a>
                </li>
                <li id="li-recordadditionalnamefonandcol5080778">
                    <a id="link-recordadditionalnamefonandcol5080778" name="link-recordadditionalnamefonandcol5080778" href="https://recherche-collection-search.bac-lac.gc.ca/eng/Home/Result?DataSource=Archives|FonAndCol&amp;SearchIn_1=Name&amp;SearchInText_1=Gilliat Eaton, Rosemary, 1919-2004&amp;SEARCH_TYPE=SEARCH_ADVANCED">Photographer: Gilliat Eaton, Rosemary, 1919-2004</a>
                </li>
        </ol>

    </div>
    <div id="jq-container-toggle-recordadditionalnamefonandcol5080778" style="display:none" aria-hidden="true">
        <button id="jq-show-link-btn-recordadditionalnamefonandcol5080778" class="CFCS-text-underline btn btn-link"
           onclick="$(this).hide(); $('#jq-hide-link-btn-recordadditionalnamefonandcol5080778').show(); $('#jq-container-body-recordadditionalnamefonandcol5080778').removeClass('CFCS-Text-truncated-2l', 200);"
           style="text-decoration:none; padding: 0;">Show more</button>
        <button id="jq-hide-link-btn-recordadditionalnamefonandcol5080778" class="CFCS-text-underline btn btn-link"
           onclick="$(this).hide(); $('#jq-show-link-btn-recordadditionalnamefonandcol5080778').show(); $('#jq-container-body-recordadditionalnamefonandcol5080778').addClass('CFCS-Text-truncated-2l', 200);"
           style="text-decoration:none; padding: 0;">Show less</button>
    </div>
        <span id="jq-container-script-recordadditionalnamefonandcol5080778">
            <script>
let textLengthrecordadditionalnamefonandcol5080778 = $('#jq-container-body-recordadditionalnamefonandcol5080778').text().length;
let containerHeightrecordadditionalnamefonandcol5080778 = $('#jq-container-body-recordadditionalnamefonandcol5080778').outerHeight();
if (containerHeightrecordadditionalnamefonandcol5080778 > 90) {
  $('#jq-container-toggle-recordadditionalnamefonandcol5080778').show();
  $('#jq-show-link-btn-recordadditionalnamefonandcol5080778').show();
  $('#jq-hide-link-btn-recordadditionalnamefonandcol5080778').hide();
  $('#jq-container-body-recordadditionalnamefonandcol5080778').addClass('CFCS-Text-truncated-2l');
} else {
  $('#jq-container-toggle-recordadditionalnamefonandcol5080778').hide();
  $('#jq-show-link-btn-recordadditionalnamefonandcol5080778').hide();
  $('#jq-hide-link-btn-recordadditionalnamefonandcol5080778').hide();
  $('#jq-container-body-recordadditionalnamefonandcol5080778').removeClass('CFCS-Text-truncated-2l');
}
</script>
        </span>

        </div>
    </div>

            


                

    <div class="CFCS-table-row-flex">
        <div id="dt-additionalinfofonandcol5080778" class="CFCS-row-label ">Additional information:</div>
        <div id="dd-additionalinfofonandcol5080778" class="CFCS-row-value ">
                

    <div id="jq-container-body-additionalinfofonandcol5080778" class="CFCS-field-container" style="">
                    

    <div class="CFCS-table-row-flex">
        <div id="dt-recordnotecode50textfonandcol5080778" class="CFCS-row-label ">General note:</div>
        <div id="dd-recordnotecode50textfonandcol5080778" class="CFCS-row-value ">
The location of the photograph and names are based on information provided by Project Naming. The goal of this project is to identify and record the names of people, places, activities and events in photographs held at Library and Archives Canada.        </div>
    </div>

                    

    <div class="CFCS-table-row-flex">
        <div id="dt-recordnotecode306textfonandcol5080778" class="CFCS-row-label ">Source of title:</div>
        <div id="dd-recordnotecode306textfonandcol5080778" class="CFCS-row-value ">
Title supplied by the archivist.        </div>
    </div>

                    

    <div class="CFCS-table-row-flex">
        <div id="dt-recordserietitlefonandcol5080778" class="CFCS-row-label ">Series added entry:</div>
        <div id="dd-recordserietitlefonandcol5080778" class="CFCS-row-value ">
Unknown Arctic        </div>
    </div>

    </div>
    <div id="jq-container-toggle-additionalinfofonandcol5080778" style="display:none" aria-hidden="true">
        <button id="jq-show-link-btn-additionalinfofonandcol5080778" class="CFCS-text-underline btn btn-link"
           onclick="$(this).hide(); $('#jq-hide-link-btn-additionalinfofonandcol5080778').show(); $('#jq-container-body-additionalinfofonandcol5080778').removeClass('CFCS-Text-truncated-2l', 200);"
           style="text-decoration:none; padding: 0;">Show more</button>
        <button id="jq-hide-link-btn-additionalinfofonandcol5080778" class="CFCS-text-underline btn btn-link"
           onclick="$(this).hide(); $('#jq-show-link-btn-additionalinfofonandcol5080778').show(); $('#jq-container-body-additionalinfofonandcol5080778').addClass('CFCS-Text-truncated-2l', 200);"
           style="text-decoration:none; padding: 0;">Show less</button>
    </div>

        </div>
    </div>

            


            


            


            

    <div class="CFCS-table-row-flex">
        <div id="dt-recordsubjectheadingfonandcol5080778" class="CFCS-row-label ">Subject heading:</div>
        <div id="dd-recordsubjectheadingfonandcol5080778" class="CFCS-row-value ">
                

    <div id="jq-container-body-recordsubjectheadingfonandcol5080778" class="CFCS-field-container" style="">
            

        <ol id="ol-recordsubjectheadingfonandcol5080778" class="">
                <li id="li-recordsubjectheadingfonandcol5080778">
                    <a id="link-recordsubjectheadingfonandcol5080778" name="link-recordsubjectheadingfonandcol5080778" href="https://recherche-collection-search.bac-lac.gc.ca/eng/Home/Result?DataSource=Archives|FonAndCol&amp;SearchIn_1=Subject&amp;SearchInText_1=Portraits&amp;SEARCH_TYPE=SEARCH_ADVANCED">Portraits</a>
                </li>
                <li id="li-recordsubjectheadingfonandcol5080778">
                    <a id="link-recordsubjectheadingfonandcol5080778" name="link-recordsubjectheadingfonandcol5080778" href="https://recherche-collection-search.bac-lac.gc.ca/eng/Home/Result?DataSource=Archives|FonAndCol&amp;SearchIn_1=Subject&amp;SearchInText_1=Inuit - Nunavut&amp;SEARCH_TYPE=SEARCH_ADVANCED">Inuit - Nunavut</a>
                </li>
                <li id="li-recordsubjectheadingfonandcol5080778">
                    <a id="link-recordsubjectheadingfonandcol5080778" name="link-recordsubjectheadingfonandcol5080778" href="https://recherche-collection-search.bac-lac.gc.ca/eng/Home/Result?DataSource=Archives|FonAndCol&amp;SearchIn_1=Subject&amp;SearchInText_1=Inuits Nunavut&amp;SEARCH_TYPE=SEARCH_ADVANCED">Inuits Nunavut</a>
                </li>
        </ol>

    </div>
    <div id="jq-container-toggle-recordsubjectheadingfonandcol5080778" style="display:none" aria-hidden="true">
        <button id="jq-show-link-btn-recordsubjectheadingfonandcol5080778" class="CFCS-text-underline btn btn-link"
           onclick="$(this).hide(); $('#jq-hide-link-btn-recordsubjectheadingfonandcol5080778').show(); $('#jq-container-body-recordsubjectheadingfonandcol5080778').removeClass('CFCS-Text-truncated-2l', 200);"
           style="text-decoration:none; padding: 0;">Show more</button>
        <button id="jq-hide-link-btn-recordsubjectheadingfonandcol5080778" class="CFCS-text-underline btn btn-link"
           onclick="$(this).hide(); $('#jq-show-link-btn-recordsubjectheadingfonandcol5080778').show(); $('#jq-container-body-recordsubjectheadingfonandcol5080778').addClass('CFCS-Text-truncated-2l', 200);"
           style="text-decoration:none; padding: 0;">Show less</button>
    </div>
        <span id="jq-container-script-recordsubjectheadingfonandcol5080778">
            <script>
let textLengthrecordsubjectheadingfonandcol5080778 = $('#jq-container-body-recordsubjectheadingfonandcol5080778').text().length;
let containerHeightrecordsubjectheadingfonandcol5080778 = $('#jq-container-body-recordsubjectheadingfonandcol5080778').outerHeight();
if (containerHeightrecordsubjectheadingfonandcol5080778 > 90) {
  $('#jq-container-toggle-recordsubjectheadingfonandcol5080778').show();
  $('#jq-show-link-btn-recordsubjectheadingfonandcol5080778').show();
  $('#jq-hide-link-btn-recordsubjectheadingfonandcol5080778').hide();
  $('#jq-container-body-recordsubjectheadingfonandcol5080778').addClass('CFCS-Text-truncated-2l');
} else {
  $('#jq-container-toggle-recordsubjectheadingfonandcol5080778').hide();
  $('#jq-show-link-btn-recordsubjectheadingfonandcol5080778').hide();
  $('#jq-hide-link-btn-recordsubjectheadingfonandcol5080778').hide();
  $('#jq-container-body-recordsubjectheadingfonandcol5080778').removeClass('CFCS-Text-truncated-2l');
}
</script>
        </span>

        </div>
    </div>

            

    <div class="CFCS-table-row-flex">
        <div id="dt-recordrecordsourceenfonandcol5080778" class="CFCS-row-label ">Source:</div>
        <div id="dd-recordrecordsourceenfonandcol5080778" class="CFCS-row-value ">
Private        </div>
    </div>

            


            


            


            


            


            


            


    </div>

                        </div>
                    </div>
                    <div id="jq-myresearch-fonandcol5080778" class="CFCS-table-cell-flex text-center CFCS-width-100px CFCS-v-align-top">
                            <a class="btn btn-default wb-lbx CFCS-margin-5-0-5-0" href="#link-to-this-rec" onclick="let recUrl=UpdateUrlEcopy('http://central.bac-lac.gc.ca/.redirect?app=fonandcol&id=5080778&lang=eng&ecopy=e010836176-v8'); $('#jq-rec-url').val(recUrl); AdjustPopupCss();" role="button" title="Link to this record"><i class="fas fa-link CFCS-font-size-2em"><span class="wb-inv">Link to this record</span></i><div class="CFCS-font-size-08em">Link to this record</div></a>
                            <br />
                            <a id="jq-add-to-research-fonandcol-5080778" class="btn btn-default wb-lbx CFCS-margin-5-0-5-0" href="#section-require-login" onclick="SetPendingAddToResearch('fonandcol-5080778'); AdjustPopupCss();" role="button" title="Sign in to start saving records using My Research"><i class="fas fa-folder-plus CFCS-font-size-2em"><span class="wb-inv">Sign in to start saving records using My Research</span></i><div class="CFCS-font-size-08em">Add to My Research</div></a>
                            
<section id="link-to-this-rec" class="mfp-hide modal-dialog modal-content overlay-def">
    <header class="modal-header"><h2 class="modal-title" id="lbx-title">Link to this record</h2></header>
    <div class="modal-body">
        <div>This link identifies the web page describing this particular record. Unlike the temporary link in your browser, this link will allow you to access, and reference, this page in the future.&#xD;&#xA;To link to this descriptive record, copy and paste the URL where ever needed (wiki, blog, document).</div>
        <div class="allWidth" style="margin-top: 10px; font-style: italic;">
            <input type="text" id="jq-rec-url" value="" class="form-control CFCS-inline-block" style="min-width:90%" />
            <i class="fas fa-copy CFCS-color-bluegrey" style="cursor: pointer; font-size: 1.5em;" title="Copy the link to the clipboard" onclick="$('#jq-rec-url').select();document.execCommand('copy');"></i>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" id="jq-button-login-close" class="btn btn-default popup-modal-dismiss">Close</button>
    </div>
    <button type="button" title="Close" class="mfp-close">×<span class="wb-inv">Close</span></button>
</section>
                                
    <section id="section-require-login" class="mfp-hide modal-dialog modal-content overlay-def">
        <header class="modal-header"><h2 class="modal-title" id="lbx-title">Sign in</h2></header>
        <div class="modal-body">
            Sign in to start saving records using My Research
        </div>
        <div class="modal-footer">
            <a id="jq-button-login" class="btn btn-primary" href="/eng/account/login?returnUrl=https%3a%2f%2frecherche-collection-search.bac-lac.gc.ca%2feng%2fHome%2fRecord%3fapp%3dfonandcol%26IdNumber%3d5080778%26ecopy%3de010836176-v8" onclick="TriggerPopupAddToResearch()">Sign in</a>
            <button type="button" id="jq-button-login-cancel" class="btn btn-default popup-modal-dismiss">Cancel</button>
        </div>
        <button type="button" title="Close" class="mfp-close">×<span class="wb-inv">Close</span></button>
    </section>    

                    </div>
                </div>
            </div>
        </section>
    </li>

                    

        <li>
            <section>
                <a href="javascript:;" id="jq-ordering-a" class="CFCS-toggle CFCS-toggle-opened" aria-expanded="true" data-target="#jq-ordering" aria-controls="jq-ordering">
                    <i class="fas fa-angle-down CFCS-icon-down"></i>
                    <i class="fas fa-angle-right CFCS-icon-right"></i>
                    Ordering and viewing options
                </a>
                <div id="jq-ordering" class="CFCS-collapsible" role="region" tabindex="-1">
                        

    <div id="dl-ordering-fonandcol5080778" class="CFCS-margin-b-0 CFCS-table-flex">
            

    <div class="CFCS-table-row-flex">
        <div id="dt-recordmediafonandcol5080778" class="CFCS-row-label ">Conditions of access:</div>
        <div id="dd-recordmediafonandcol5080778" class="CFCS-row-value ">
                

    <div id="jq-container-body-recordmediafonandcol5080778" class="CFCS-field-container" style="">
            

            

    <div id="media-1200-block" style="/margin-bottom: 15px">
        <div id="media-1200-text" class="CFCS-coa-all">
            <div id="media-1200-left" class="CFCS-coa-left">
                <strong><em>Graphic (photo)</em></strong>
            </div>
            <div id="media-1200-right" class="CFCS-coa-right">
                [Consultation<span class="hide-on-mobile"> 90</span> Open]
            </div>
        </div>
                

    <div id="physicalManifestation-2-block">
        <div id="physicalManifestation-2-text" class="CFCS-coa-all">
            <div id="physicalManifestation-2-text-left" class="CFCS-coa-left" style="padding-left: 15px">
                <strong><em>Volume</em></strong> [R12438]  
            </div>
            <div id="physicalManifestation-2-text-right" class="CFCS-coa-right" >
                [Consultation<span class="hide-on-mobile"> 90</span> Open]
            </div>
        </div>
                <div id="subcontainer-1-block">
                    <div id="subcontainer-1-text" class="CFCS-coa-all">
                        <div id="subcontainer-1-text-left" class="CFCS-coa-left" style="padding-left: 45px">
                            9 File number 1
                        </div>
                        <div id="subcontainer-1-text-right" class="CFCS-coa-right">
                            [Consultation<span class="hide-on-mobile"> 90</span> Open]
                        </div>
                    </div>
                </div>
    </div>

    </div>


    </div>
    <div id="jq-container-toggle-recordmediafonandcol5080778" style="display:none" aria-hidden="true">
        <button id="jq-show-link-btn-recordmediafonandcol5080778" class="CFCS-text-underline btn btn-link"
           onclick="$(this).hide(); $('#jq-hide-link-btn-recordmediafonandcol5080778').show(); $('#jq-container-body-recordmediafonandcol5080778').removeClass('CFCS-Text-truncated-2l', 200);"
           style="text-decoration:none; padding: 0;">Show more</button>
        <button id="jq-hide-link-btn-recordmediafonandcol5080778" class="CFCS-text-underline btn btn-link"
           onclick="$(this).hide(); $('#jq-show-link-btn-recordmediafonandcol5080778').show(); $('#jq-container-body-recordmediafonandcol5080778').addClass('CFCS-Text-truncated-2l', 200);"
           style="text-decoration:none; padding: 0;">Show less</button>
    </div>
        <span id="jq-container-script-recordmediafonandcol5080778">
            <script>
let textLengthrecordmediafonandcol5080778 = $('#jq-container-body-recordmediafonandcol5080778').text().length;
let containerHeightrecordmediafonandcol5080778 = $('#jq-container-body-recordmediafonandcol5080778').outerHeight();
if (containerHeightrecordmediafonandcol5080778 > 90) {
  $('#jq-container-toggle-recordmediafonandcol5080778').show();
  $('#jq-show-link-btn-recordmediafonandcol5080778').show();
  $('#jq-hide-link-btn-recordmediafonandcol5080778').hide();
  $('#jq-container-body-recordmediafonandcol5080778').addClass('CFCS-Text-truncated-2l');
} else {
  $('#jq-container-toggle-recordmediafonandcol5080778').hide();
  $('#jq-show-link-btn-recordmediafonandcol5080778').hide();
  $('#jq-hide-link-btn-recordmediafonandcol5080778').hide();
  $('#jq-container-body-recordmediafonandcol5080778').removeClass('CFCS-Text-truncated-2l');
}
</script>
        </span>

        </div>
    </div>

            

    <div class="CFCS-table-row-flex">
        <div id="dt-recordnotecode111textfonandcol5080778" class="CFCS-row-label ">Terms of use:</div>
        <div id="dd-recordnotecode111textfonandcol5080778" class="CFCS-row-value ">
                

    <div id="jq-container-body-recordnotecode111textfonandcol5080778" class="CFCS-field-container" style="">
            <span>Credit: Rosemary Gilliat Eaton / Rosemary Gilliat Eaton fonds / Library and Archives Canada / e010836176 ; Copyright: Copyright has been assigned to Library and Archives Canada by the copyright holder, the Estate of Rosemary Gilliat Eaton ; Restrictions on use: Nil.</span>
    </div>
    <div id="jq-container-toggle-recordnotecode111textfonandcol5080778" style="display:none" aria-hidden="true">
        <button id="jq-show-link-btn-recordnotecode111textfonandcol5080778" class="CFCS-text-underline btn btn-link"
           onclick="$(this).hide(); $('#jq-hide-link-btn-recordnotecode111textfonandcol5080778').show(); $('#jq-container-body-recordnotecode111textfonandcol5080778').removeClass('CFCS-Text-truncated-2l', 200);"
           style="text-decoration:none; padding: 0;">Show more</button>
        <button id="jq-hide-link-btn-recordnotecode111textfonandcol5080778" class="CFCS-text-underline btn btn-link"
           onclick="$(this).hide(); $('#jq-show-link-btn-recordnotecode111textfonandcol5080778').show(); $('#jq-container-body-recordnotecode111textfonandcol5080778').addClass('CFCS-Text-truncated-2l', 200);"
           style="text-decoration:none; padding: 0;">Show less</button>
    </div>
        <span id="jq-container-script-recordnotecode111textfonandcol5080778">
            <script>
let textLengthrecordnotecode111textfonandcol5080778 = $('#jq-container-body-recordnotecode111textfonandcol5080778').text().length;
let containerHeightrecordnotecode111textfonandcol5080778 = $('#jq-container-body-recordnotecode111textfonandcol5080778').outerHeight();
if (containerHeightrecordnotecode111textfonandcol5080778 > 90) {
  $('#jq-container-toggle-recordnotecode111textfonandcol5080778').show();
  $('#jq-show-link-btn-recordnotecode111textfonandcol5080778').show();
  $('#jq-hide-link-btn-recordnotecode111textfonandcol5080778').hide();
  $('#jq-container-body-recordnotecode111textfonandcol5080778').addClass('CFCS-Text-truncated-2l');
} else {
  $('#jq-container-toggle-recordnotecode111textfonandcol5080778').hide();
  $('#jq-show-link-btn-recordnotecode111textfonandcol5080778').hide();
  $('#jq-hide-link-btn-recordnotecode111textfonandcol5080778').hide();
  $('#jq-container-body-recordnotecode111textfonandcol5080778').removeClass('CFCS-Text-truncated-2l');
}
</script>
        </span>

        </div>
    </div>

    </div>

                        <div class="CFCS-width-all alert alert-info" style="font-size: .8em">
                            <p>
                                You can
                                <a href="https://library-archives.canada.ca/eng/services/public/consult-lac-material/order-archival-material/Pages/order-archival-materials.aspx" target="_blank" class="alert-link">order materials in advance</a>
                                to be ready for you when you
                                <a href="https://library-archives.canada.ca/eng/services/public/visit/Pages/visit-us.aspx" target="_blank" class="alert-link">visit</a>.
                                You will need a
                                <a href="https://library-archives.canada.ca/eng/services/public/pages/registration-intro.aspx" target="_blank" class="alert-link">user card</a>
                                to do this.
                            </p>
                            <p>
                                Cannot visit us on site? You can
                                <a href="https://reproduction.bac-lac.gc.ca/en" target="_blank" class="alert-link">purchase a copy</a>
                                to be sent to you. Some restrictions may apply.
                            </p>
                        </div>
                </div>
            </section>
        </li>

                </ul>
                

            </div>

<script src="/js/record-display.js?v=03Oh69AgEYCG49UYhZwLk2j1W4E"></script>


        <div class="CFCS-width-all CFCS-debug CFCS-display-none">
            <div class="CFCS-display-table-row CFCS-background-eee">
                <div class="CFCS-display-table-cell"><strong>Event</strong></div>
                <div class="CFCS-display-table-cell"><strong>Elapsed time (mm:ss,...)</strong></div>
            </div>
                <div class="CFCS-display-table-row">
                    <div class="CFCS-display-table-cell CFCS-div-indent-0">QueryString parameter Parsing</div>
                    <div class="CFCS-display-table-cell">00:00.00</div>
                </div>
                <div class="CFCS-display-table-row">
                    <div class="CFCS-display-table-cell CFCS-div-indent-0">Load Record model</div>
                    <div class="CFCS-display-table-cell">00:00.00</div>
                </div>
                <div class="CFCS-display-table-row">
                    <div class="CFCS-display-table-cell CFCS-div-indent-1">Load record XML - Archives|FonAndCol</div>
                    <div class="CFCS-display-table-cell">00:00.00</div>
                </div>
                <div class="CFCS-display-table-row">
                    <div class="CFCS-display-table-cell CFCS-div-indent-1">Load record fields from record XML</div>
                    <div class="CFCS-display-table-cell">00:00.00</div>
                </div>
                <div class="CFCS-display-table-row">
                    <div class="CFCS-display-table-cell CFCS-div-indent-1">Load record ecopies from the DB</div>
                    <div class="CFCS-display-table-cell">00:00.00</div>
                </div>
                <div class="CFCS-display-table-row">
                    <div class="CFCS-display-table-cell CFCS-div-indent-0">Load KWIC from Search API</div>
                    <div class="CFCS-display-table-cell">00:00.00</div>
                </div>
                <div class="CFCS-display-table-row">
                    <div class="CFCS-display-table-cell CFCS-div-indent-0">Load previous / next record ids from the result list</div>
                    <div class="CFCS-display-table-cell">00:00.00</div>
                </div>
                <div class="CFCS-display-table-row">
                    <div class="CFCS-display-table-cell CFCS-div-indent-0">Save record stats</div>
                    <div class="CFCS-display-table-cell">00:00.00</div>
                </div>
                <div class="CFCS-display-table-row">
                    <div class="CFCS-display-table-cell CFCS-div-indent-0">Load Record display view</div>
                    <div class="CFCS-display-table-cell">00:00.00</div>
                </div>
        </div>


        <div id="def-preFooter">
        </div>
        <!-- Write closure template -->
        <script>
            var defPreFooter = document.getElementById("def-preFooter");
            var defPreFooterOuterHTML = wet.builder.preFooter({
  "dateModified": "2025-05-06",
  "pageDetails": true,
  "showFeedback": {
    "enabled": false
  },
  "showPostContent": false,
  "showShare": false
});


            defPreFooter.outerHTML = defPreFooterOuterHTML;
        </script>
     </main>

    <div id="def-footer">
    </div>
    <!-- Write closure template -->
    <script>
        var defFooter = document.getElementById("def-footer");
        

                defFooter.outerHTML = wet.builder.footer({
  "footerSections": {
    "title": "Library and Archives Canada",
    "links": [
      {
        "text": "Ask us a question",
        "href": "https://library-archives.canada.ca/eng/services/public/ask-us-question/pages/ask-us-question.aspx",
        "newWindow": true
      },
      {
        "text": "Give feedback",
        "href": "https://library-archives.canada.ca/eng/Pages/feedback.aspx",
        "newWindow": true
      }
    ]
  },
  "contextualFooter": {
    "title": "Library and Archives Canada",
    "links": [
      {
        "text": "Ask us a question",
        "href": "https://library-archives.canada.ca/eng/services/public/ask-us-question/pages/ask-us-question.aspx",
        "newWindow": true
      },
      {
        "text": "Give feedback",
        "href": "https://library-archives.canada.ca/eng/Pages/feedback.aspx",
        "newWindow": true
      }
    ]
  }
});
                </script>

        <div id="def-accountMenu" class="hidden">
             

        
        <li>
            <a href="/eng/account/login?returnUrl=%2Feng%2FHome%2FRecord%3Fapp%3Dfonandcol%26IdNumber%3D5080778%26ecopy%3De010836176-v8" class="btn" role="button"><span class="fas fa-sign-in-alt fa-fw" aria-hidden="true"></span> Sign in</a>
        </li>

        </div>
    <div id="def-breadcrumb" class="hidden">
        
    <li><a href="https://library-archives.canada.ca/eng/collection/Pages/collection.aspx">Collection</a></li>
    <li><a href="https://library-archives.canada.ca/eng/collection/search-collections/pages/search-the-collection.aspx">Search the collections</a></li>
    

    </div>

        <div id="def-menu" class="hidden">
            

        </div>

    
    
        <!--[if gte IE 9 | !IE ]><!-->
        <script src="https://www.canada.ca/etc/designs/canada/cdts/gcweb/v5_0_2/wet-boew/js/wet-boew.min.js"></script>
        <!--<![endif]-->
        <script src="https://www.canada.ca/etc/designs/canada/cdts/gcweb/v5_0_2/wet-boew/js/theme.min.js"></script>
    
    <script src="https://www.canada.ca/etc/designs/canada/cdts/gcweb/v5_0_2/cdts/cdtscustom.js"></script>

    
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-W7F3J3NPK6"></script>
    <script src="/bootstrap-5.0.2-dist/js/popper.js"></script>
    <script src="/bootstrap-5.0.2-dist/js/bootstrap.js"></script>
    <script>
        var $ = jQuery.noConflict();

        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());
        gtag('config', 'G-W7F3J3NPK6');

        let const_AND = "AND";
        let const_OR = "OR";
        let const_NOT = "NOT";
        let vSearchCensusAdvancedUrl = "https://recherche-collection-search.bac-lac.gc.ca/eng/Census/SearchAdvanced";
        let vSearchCensusBasicUrl = "https://recherche-collection-search.bac-lac.gc.ca/eng/Census/Search";
        let vSearchAdvancedHelpUrl = "https://recherche-collection-search.bac-lac.gc.ca/eng/Help/";
        let vSearchAdvancedUrl = "https://recherche-collection-search.bac-lac.gc.ca/eng/Home/SearchAdvanced";
        let vSearchBasicUrl = "https://recherche-collection-search.bac-lac.gc.ca/eng/Home/Search";
        let vResultPageUrl = "https://recherche-collection-search.bac-lac.gc.ca/eng/Home/Result";
        let const_API_UNRESPONSIVE = "APIUNRESPONSIVE";
        let const_BLOCK_ADDITIONAL_SEARCH_CNTNR = "jq-ADD_SEARCH_CNTNR";
        let const_BLOCK_COLLAPSE_ADDITIONAL = "jq-COLLAPSE_ADDITIONAL";
        let const_BLOCK_EXPAND_ADDITIONAL = "jq-EXPAND_ADDITIONAL";
        let const_CENSUS_YEAR_CODE = "ApplicationCode";
        let const_CONTENT_TYPE = "contenttype";
        let const_CONTENT_TYPE_BASIC = "section_basic";
        let const_CONTENT_TYPE_ADDITIONAL = "section_additional";
        let const_CONTENT_TYPE_ADVANCED = "section_advanced";
        let const_DATA_SOURCE = "DataSource";
        let const_DATA_SOURCE_SELECTED = "DataSourceSel";
        let const_DATA_SOURCE_CENSUS = "Genealogy|Census";
        let const_DATA_SET = "dataset";
        let const_DIGITAL_MANIFEST_URL = "https://digitalmanifest.bac-lac.gc.ca/DigitalManifest/";
        let const_DISTRICT = "District";
        let const_DISTRICT_CODE = "DistrictCode";
        let const_DISTRICT_NAME = "DistrictName";
        let const_DISTRICT_NUMBER = "DistrictNumber";
        let const_DIVISION = "Division";
        let const_DIVISION_CODE = "DivisionCode";
        let const_DIVISION_NUMBER = "DivisionNumber";
        let const_ERR_REQ_EMAIL = "error-required-email";
        let const_ERR_INVALID_EMAIL = "error-invalid-email";
        let const_ERR_REQ_MESSAGE = "error-required-message";
        let const_ERR_UNEXPECTED = "error-unexpected";
        let const_ENV = "enviro";
        let const_FORMAT = "format";
        let const_FIRST_NAME = "FirstName";
        let const_ID_NUMBER = "IdNumber";
        let const_IS_FRENCH = "false";
        let const_KEYWORD = "q";
        let const_KEYWORD_1 = "q_1";
        let const_KEYWORD_2 = "q_2";
        let const_KEYWORD_3 = "q_3";
        let const_KEYWORD_4 = "q_4";
        let const_KEYWORD_ANY = "q_any";
        let const_KEYWORD_EXACT = "q_exact";
        let const_KEYWORD_TYPE = "q_type";
        let const_KEYWORD_TYPE_1 = "q_type_1";
        let const_KEYWORD_TYPE_2 = "q_type_2";
        let const_KEYWORD_TYPE_3 = "q_type_3";
        let const_KEYWORD_TYPE_4 = "q_type_4";
        let const_MATERIAL_DATE = "MaterialDate";
        let const_ONLINE = "OnlineCode";
        let const_OPERATOR = "Operator_";
        let const_OPERATOR_0 = "Operator_0";
        let const_OPERATOR_1 = "Operator_1";
        let const_OPERATOR_2 = "Operator_2";
        let const_OPERATOR_Q = "Operator_q_";
        let const_OPERATOR_Q_0 = "Operator_q_0";
        let const_OPERATOR_Q_1 = "Operator_q_1";
        let const_OPERATOR_Q_2 = "Operator_q_2";
        let const_OPERATOR_Q_3 = "Operator_q_3";
        let const_Q_STRING = "jq-qstring";
        let const_PLEASE_WAIT = "Please wait ...";
        let const_PROVINCE_CODE = "ProvinceCode";
        let const_RANGE_SLIDER = "jq-RANGE_SLIDER";
        let const_REC_PER_PAGE = "num";
        let const_SEARCH_CSS_AUTOCOMPLETE = "jq-autocomplete";
        let const_SEARCH_BY = "jq-SEARCH_BY";
        let const_SEARCH_IN = "SearchIn_";
        let const_SEARCH_IN_1 = "SearchIn_1";
        let const_SEARCH_IN_2 = "SearchIn_2";
        let const_SEARCH_IN_3 = "SearchIn_3";
        let const_SEARCH_IN_TXT = "SearchInText_";
        let const_SEARCH_IN_TXT_1 = "SearchInText_1";
        let const_SEARCH_IN_TXT_2 = "SearchInText_2";
        let const_SEARCH_IN_TXT_3 = "SearchInText_3";
        let const_SEARCH_UCC = "HasUccCode";
        let const_SEARCH_TYPE = "SEARCH_TYPE";
        let const_SEARCH_TYPE_BASIC = "SEARCH_BASIC";
        let const_SEARCH_TYPE_ADVANCED = "SEARCH_ADVANCED";
        let const_SEARCH_TYPE_ADVANCED_HELP = "SEARCH_ADVANCED_HELP";
        let const_SEARCH_CENSUS_TYPE_BASIC = "SEARCH_CENSUS_BASIC";
        let const_SEARCH_CENSUS_TYPE_ADVANCED = "SEARCH_CENSUS_ADVANCED";
        let const_SORT = "sort";
        let const_SPLITTER = "~";
        let const_STATUS_PROCESS_ERROR = "process-error";
        let const_STATUS_SUCCESS = "success";
        let const_STATUS_VALIDATION_ERR = "validation-error";
        let const_START_RECORD = "start";
        let const_SUB_DISTRICT = "SubDistrict";
        let const_SUB_DISTRICT_CODE = "SubDistrictCode";
        let const_SUB_DISTRICT_NAME = "SubDistrictName";
        let const_SUB_DISTRICT_NUMBER = "SubDistrictNumber";
        let lblClose = "Close";
        let lblCopyEcopy = "Copy the ecopy number to the clipboard";
        let txtDownload = "Download";
        let lblEcopyNbr = "Ecopy number:";
        let lblNew = "New";
        let lblPleaseWait = "Please wait ...";
        let txtPrint = "Print";
        let lblSelect = "Select";
        let txtAlreadyInFolder = "Already contains same record";
        let txtAddTo = "Add to";
        let vAutoCompleteDelay = "300";
        let vAutoCompleteMinLength = "3";
        let vCentralUrl = "http://central.bac-lac.gc.ca/";
        let vDateBucketId = "DateBucket";
        let vEnviro = "prod";
        let vFavourites = "favourites";
        let vLang2 = "en";
        let vLang3 = "eng";
        let vRangeClear = "Clear range";
        let vRangeSet = "Set range";
        let vRangeTypeFields = "";
        let vResearchFolderPrivate = "priv";
        let vResearchFolderPublic = "pub";
        let vResearchAccountMy = "my";
        let vResearchAccountLac = "lac";
        let errMyResearchAdd = "An error occured: the connection to MyResearch was refused";
        let errUnauthorizedMyResearchAdd = "An error occured: the connection to MyResearch was refused";
        let errNotFoundMyResearchAdd = "An error occured: the connection to MyResearch was refused";

        // Add Antiforgery token to all jQuery ajax requests
        $.ajaxSetup({
            headers: {
                'RequestVerificationToken': 'CfDJ8AtE2xFYYa1OhJ_MtXDGX3KV_57Paav7E5BsgifTDzwFPUV13G9PbTi-Z786-nMF9vIx539syqkyxFgFRqWUdIoHDmMH4MXCeMZPGAdj7ewSmvRihl0BzUoz64FrUjSgJk3fj5qWNmfbuiGQOAoaaOw'
            }
        });

        window.antiforgery = {
            name: '__RequestVerificationToken',
            value: 'CfDJ8AtE2xFYYa1OhJ_MtXDGX3KV_57Paav7E5BsgifTDzwFPUV13G9PbTi-Z786-nMF9vIx539syqkyxFgFRqWUdIoHDmMH4MXCeMZPGAdj7ewSmvRihl0BzUoz64FrUjSgJk3fj5qWNmfbuiGQOAoaaOw'
        };

        const bootstrapLink = document.createElement("link");
        bootstrapLink.setAttribute("href", "/bootstrap-5.0.2-dist/css/bootstrap.css");
        bootstrapLink.setAttribute("rel", "stylesheet");
        document.head.prepend(bootstrapLink);


        $(document).ready(function () {
            //Set session Id
            // let tabId = sessionStorage.getItem("tabId");
            // if (!tabId || (tabId !== sessionStorage.getItem("tabClosed"))) {
            //     tabId = Math.random().toString(36).substr(2) + Date.now().toString(36).substr(5);
            //     sessionStorage.setItem("tabId", tabId);
            // }
            // sessionStorage.removeItem("tabClosed");
            // window.addEventListener("unload", function () {
            //     sessionStorage.setItem("tabClosed", tabId);
            // });

            // let uilanguage = navigator.languages[0];
            // let userAgent = window.navigator.userAgent.toLowerCase();
            // let browserName = (function (agent) {
            //     switch (true) {
            //         case agent.indexOf("edge") > -1: return "MS Edge";
            //         case agent.indexOf("edg/") > -1: return "Edge ( chromium based)";
            //         case agent.indexOf("opr") > -1 && !!window.opr: return "Opera";
            //         case agent.indexOf("chrome") > -1 && !!window.chrome: return "Chrome";
            //         case agent.indexOf("trident") > -1: return "MS IE";
            //         case agent.indexOf("firefox") > -1: return "Mozilla Firefox";
            //         case agent.indexOf("safari") > -1: return "Safari";
            //         default: return "other";
            //     }
            // })(userAgent);
            // let osName = navigator.platform;
            // let deviceName = (function (agent) {
            //     switch (true) {
            //         case agent.indexOf("iphone") > -1: return "iPhone";
            //         case agent.indexOf("ipod/") > -1: return "iPod";
            //         case agent.indexOf("ipad") > -1: return "iPad";
            //         case agent.indexOf("blackberry") > -1: return "BlackBerry";
            //         case agent.indexOf("android") > -1: return "Android";
            //         case agent.indexOf("webos") > -1: return "webOS";
            //         case agent.indexOf("Windows phone") > -1: return "Windows Phone";
            //         case agent.indexOf("zunewp") > -1: return "ZuneWP7";
            //         default: return "Standard device";
            //     }
            // })(userAgent);
            // setTimeout(() => {
            //     let url = '/ajax/set-applog-data?sessionId=' + tabId + '&browserName=' + browserName + '&deviceName=' +
            //         deviceName + '&uilanguage=' + uilanguage + '&osname=' + osName;
            //     $.ajax({
            //         type: "GET",
            //         url: url,
            //         success: function (result) {
            //             console.log(result);
            //         },
            //         statusCode: {
            //             400: function () {
            //                 $('.jq-myresearch-add').hide();
            //                 $('#jq-myresearch-error').show();
            //             }
            //         }
            //     });
            // }, 500);

            let arrowDD = document.getElementById("accountDropDownMenu");
            if (arrowDD) {
                var extraArrow = arrowDD.getElementsByClassName('expicon glyphicon glyphicon-chevron-down');
                extraArrow[0].setAttribute('style', 'display:none')

                arrowDD.addEventListener('click', function (e) {
                    setTimeout(() => {
                        var ddl = document.getElementsByClassName('dropdown-menu show').item(0);
                        if (ddl != undefined || ddl == null) {
                            ddl.setAttribute('style', 'left: auto; position: absolute; transform: translate3d(0px, 45px, 0px); top: 0px; will-change: transform;');
                        }
                    }, 10)
                }, 1);
            }
        });
    </script>

    <script src="/lib/jquery-ui-1.13.2/jquery-ui.js"></script>
    <script src="/js/site.js?v=gqNgp_-adQE7ejZlS4lxoDDnwt4"></script>

    
    <script src="/js/my-research.js?v=Shia4kW8XYZo77A-gPkBwOThv4s"></script>
    <script>
        var vMyResearchVersion = "1";
        $('#wb-cont').html('[Osuitok Ipeelee, Kinngait, Nunavut]');
    </script>
    
    <script type="text/javascript">_satellite.pageBottom();</script>


    <script type="text/javascript">
        if (typeof ($.fn.dropdown) === 'undefined') {
            document.write('<script src="/GcWeb/js/bootstrap-dropdown.min.js"><\/script>');
        }
    </script>

        <script>_satellite.pageBottom();</script>

    <script>
        $(document).ready(function(){
            // Append breadcrumbs
            var $defBreadcrumb = $("#def-breadcrumb");
            if ($defBreadcrumb.length) {
                $("ol.breadcrumb").append($defBreadcrumb.html());
                $defBreadcrumb.remove();
            }

            // Add menu
            var $defMenu = $("#def-menu");
            if ($defMenu.length) {
                $("#wb-sm").get(0).outerHTML = $defMenu.html();
                $defMenu.remove();
            }

            // Replace account menu
            var $defAccountMenu = $("#def-accountMenu");
            if ($defAccountMenu.length) {
                $(".app-list-account").html($defAccountMenu.html());
                $(".app-bar #accountDropDownMenu").closest("nav").attr("id", "gc-analytics-dnt");
                $(".app-bar-mb #accountDropDownMenu").closest("nav").attr("id", "gc-analytics-dnt2");
                $(".app-bar-mb #accountDropDownMenu").attr("id", "accountDropDownMenu-mb");
                $(".app-bar-mb [aria-labelledby='accountDropDownMenu']").attr("aria-labelledby", "accountDropDownMenu-mb");
                $defAccountMenu.remove();
            }

        });
    </script>

</body>
</html>