import os
import pandas as pd
import boto3
from dotenv import load_dotenv
from pathlib import Path
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# AWS Configuration
AWS_ACCESS_KEY = os.getenv('AWS_ACCESS_KEY')
AWS_SECRET = os.getenv('AWS_SECRET')
AWS_S3_BUCKET = os.getenv('AWS_S3_BUCKET')
AWS_S3_REGION = os.getenv('AWS_S3_REGION')

# Validate AWS configuration
if not all([AWS_ACCESS_KEY, AWS_SECRET, AWS_S3_BUCKET, AWS_S3_REGION]):
    logger.error("Error: AWS credentials or configuration not found in environment variables.")
    logger.error("Make sure AWS_ACCESS_KEY, AWS_SECRET, AWS_S3_BUCKET, and AWS_S3_REGION are set.")
    exit(1)

# Initialize S3 client
s3_client = boto3.client(
    's3',
    aws_access_key_id=AWS_ACCESS_KEY,
    aws_secret_access_key=AWS_SECRET,
    region_name=AWS_S3_REGION
)

def extract_qr_code_filename(qr_path):
    """Extract the filename from QR code path"""
    # Handle both forward and backward slashes
    return os.path.basename(qr_path.replace('\\', '/'))

def upload_file_to_s3(local_path, s3_key):
    """Upload a file to S3"""
    try:
        if not os.path.exists(local_path):
            logger.error(f"File not found: {local_path}")
            return None
            
        # Remove leading slash if present
        if s3_key.startswith('/'):
            s3_key = s3_key[1:]
            
        # Determine content type based on file extension
        content_type = None
        if local_path.lower().endswith('.png'):
            content_type = 'image/png'
        elif local_path.lower().endswith('.jpg') or local_path.lower().endswith('.jpeg'):
            content_type = 'image/jpeg'
            
        extra_args = {}
        if content_type:
            extra_args['ContentType'] = content_type
            
        logger.info(f"Uploading to S3: s3://{AWS_S3_BUCKET}/{s3_key}")
        s3_client.upload_file(
            local_path,
            AWS_S3_BUCKET,
            s3_key,
            ExtraArgs=extra_args
        )
        
        s3_url = f"https://{AWS_S3_BUCKET}.s3.{AWS_S3_REGION}.amazonaws.com/{s3_key}"
        logger.info(f"Successfully uploaded to {s3_url}")
        return s3_url
        
    except Exception as e:
        logger.error(f"Error uploading {local_path}: {str(e)}")
        return None

def process_csv():
    # Read the CSV file
    csv_path = "CMH_Photos/CMH_Photo.csv"
    df = pd.read_csv(csv_path)
    
    # Create new columns for S3 URLs
    df['QR_Code_S3_URL'] = None
    df['Image_S3_URL'] = None
    
    # Process each row
    total_rows = len(df)
    for index, row in df.iterrows():
        try:
            # Process QR Code
            qr_filename = extract_qr_code_filename(row['QR_Code_Path'])
            qr_local_path = os.path.join("CMH_Photos", "qr_codes_Photos", qr_filename)
            qr_s3_key = f"qr_codes/{qr_filename}"
            qr_s3_url = upload_file_to_s3(qr_local_path, qr_s3_key)
            df.at[index, 'QR_Code_S3_URL'] = qr_s3_url
            
            # Process Image
            image_local_path = os.path.join("CMH_Photos", "Photos", row['Image_Path'])
            image_s3_key = f"images/{row['Image_Path']}"
            image_s3_url = upload_file_to_s3(image_local_path, image_s3_key)
            df.at[index, 'Image_S3_URL'] = image_s3_url
            
            if (index + 1) % 100 == 0:
                logger.info(f"Processed {index + 1}/{total_rows} rows ({(index + 1)/total_rows*100:.1f}%)")
                
        except Exception as e:
            logger.error(f"Error processing row {index}: {str(e)}")
            continue
    
    # Save the new CSV with S3 URLs
    output_path = "CMH_Photos/CMH_Photo_with_s3_urls.csv"
    df.to_csv(output_path, index=False)
    logger.info(f"Processing complete. New CSV saved to {output_path}")

if __name__ == "__main__":
    process_csv()