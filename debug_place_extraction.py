#!/usr/bin/env python3
"""
Debug script to analyze place field extraction from Library and Archives Canada website
"""
import requests
import re
from bs4 import BeautifulSoup

def analyze_place_extraction(url):
    """Analyze how place information is structured in the HTML"""
    print(f"Analyzing URL: {url}")
    print("=" * 80)
    
    try:
        response = requests.get(url)
        html = response.text
        soup = BeautifulSoup(html, 'html.parser')
        
        print("\n1. Looking for CFCS-row-label and CFCS-row-value pairs:")
        print("-" * 50)
        
        # Find all label-value pairs
        label_divs = soup.find_all('div', class_=lambda c: c and 'CFCS-row-label' in c)
        for label_div in label_divs:
            label_text = label_div.text.strip().lower().rstrip(':')
            print(f"Label found: '{label_text}'")
            
            # Find corresponding value div
            value_div = label_div.find_next_sibling('div', class_=lambda c: c and 'CFCS-row-value' in c)
            if value_div:
                value_text = value_div.text.strip()
                print(f"  Value: '{value_text}'")
                
                # Check if this looks like a place field
                if "place" in label_text and "creation" not in label_text:
                    print(f"  *** PLACE FIELD FOUND: '{value_text}' ***")
            print()
        
        print("\n2. Looking for specific place patterns:")
        print("-" * 50)
        
        # Look for place-specific patterns
        place_patterns = [
            r'Place:?\s*</div>\s*<div[^>]*class="[^"]*CFCS-row-value[^"]*"[^>]*>\s*([^<]+)',
            r'<div[^>]*class="[^"]*CFCS-row-label[^"]*"[^>]*>Place:?</div>\s*<div[^>]*class="[^"]*CFCS-row-value[^"]*"[^>]*>([^<]+)',
            r'recordnotecode24placeofpublicationfonandcol[^>]*>\s*([^<]+)',
        ]
        
        for i, pattern in enumerate(place_patterns, 1):
            matches = re.findall(pattern, html, re.IGNORECASE | re.DOTALL)
            print(f"Pattern {i}: {pattern}")
            if matches:
                for match in matches:
                    clean_match = match.strip()
                    print(f"  Match found: '{clean_match}'")
            else:
                print("  No matches found")
            print()
        
        print("\n3. Looking for all divs containing 'place':")
        print("-" * 50)
        
        # Find all divs that contain the word "place"
        place_divs = soup.find_all('div', string=re.compile(r'place', re.IGNORECASE))
        for div in place_divs:
            print(f"Div text: '{div.text.strip()}'")
            print(f"Div classes: {div.get('class', [])}")
            print(f"Div id: {div.get('id', 'No ID')}")
            
            # Look for sibling or nearby value
            next_div = div.find_next_sibling('div')
            if next_div:
                print(f"Next sibling text: '{next_div.text.strip()}'")
                print(f"Next sibling classes: {next_div.get('class', [])}")
            print()
        
        print("\n4. Raw HTML snippet around place fields:")
        print("-" * 50)
        
        # Find place-related HTML snippets
        place_html_matches = re.findall(r'.{0,200}Place:?.{0,200}', html, re.IGNORECASE | re.DOTALL)
        for i, match in enumerate(place_html_matches[:3], 1):  # Show first 3 matches
            print(f"HTML snippet {i}:")
            print(match.strip())
            print()
            
    except Exception as e:
        print(f"Error analyzing URL: {e}")

def test_current_extraction_logic(url):
    """Test the current extraction logic from the scraper"""
    print(f"\n5. Testing current scraper logic on: {url}")
    print("-" * 50)
    
    try:
        response = requests.get(url)
        html = response.text
        soup = BeautifulSoup(html, 'html.parser')
        
        # Simulate the current scraper logic
        place_found = ""
        
        # Look for CFCS-row-label and CFCS-row-value pairs (current logic)
        label_divs = soup.find_all('div', class_=lambda c: c and 'CFCS-row-label' in c)
        for label_div in label_divs:
            label = label_div.text.strip().lower().rstrip(':')
            value_div = label_div.find_next_sibling('div', class_=lambda c: c and 'CFCS-row-value' in c)
            
            if value_div:
                value = value_div.text.strip()
                print(f"Current logic - Label: '{label}', Value: '{value}'")
                
                # Apply current place detection logic
                if "place" in label and "creation" not in label:
                    place_found = value
                    print(f"  *** Current logic would extract: '{place_found}' ***")
        
        if not place_found:
            print("Current logic found no place field!")
        
        return place_found
        
    except Exception as e:
        print(f"Error testing current logic: {e}")
        return ""

if __name__ == "__main__":
    # Test URLs from the data
    test_urls = [
        "https://recherche-collection-search.bac-lac.gc.ca/eng/Home/Record?app=fonandcol&IdNumber=5080778&q_type_1=q&q_1=Nunavut&ecopy=e010836176-v8",
        "https://recherche-collection-search.bac-lac.gc.ca/eng/Home/Record?app=fonandcol&IdNumber=5081121&q_type_1=q&q_1=Nunavut&ecopy=e010868837-v8",
        "https://recherche-collection-search.bac-lac.gc.ca/eng/Home/Record?app=fonandcol&IdNumber=4725697&q_type_1=q&q_1=Nunavut&ecopy=e011153909-v8"
    ]
    
    for url in test_urls:
        analyze_place_extraction(url)
        test_current_extraction_logic(url)
        print("\n" + "=" * 80 + "\n")
