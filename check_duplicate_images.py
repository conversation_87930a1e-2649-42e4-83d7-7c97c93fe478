import pandas as pd
import logging
from collections import defaultdict

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def process_image_paths(image_path):
    """Process image paths that might contain multiple images"""
    if pd.isna(image_path):
        return []
    
    # Split by comma and strip whitespace
    return [path.strip() for path in str(image_path).split(',')]

def check_duplicates():
    # Read the CSV file
    csv_path = "CMH_Photos/CMH_Photo_with_s3_urls_updated.csv"
    df = pd.read_csv(csv_path)
    
    # Dictionary to store image paths and their row numbers
    image_paths = defaultdict(list)
    
    # Process each row
    for index, row in df.iterrows():
        # Check QR code paths
        if not pd.isna(row['QR_Code_Path']):
            qr_path = row['QR_Code_Path']
            image_paths[qr_path].append(index)
        
        # Check image paths
        if not pd.isna(row['Image_Path']):
            paths = process_image_paths(row['Image_Path'])
            for path in paths:
                image_paths[path].append(index)
    
    # Find duplicates
    duplicates = {path: rows for path, rows in image_paths.items() if len(rows) > 1}
    
    if duplicates:
        logger.info(f"Found {len(duplicates)} duplicate image paths:")
        for path, rows in duplicates.items():
            logger.info(f"\nPath: {path}")
            logger.info(f"Found in rows: {rows}")
            
            # Show some context for each duplicate
            for row in rows:
                logger.info(f"Row {row} context:")
                logger.info(f"  QR_Code_Path: {df.iloc[row]['QR_Code_Path']}")
                logger.info(f"  Image_Path: {df.iloc[row]['Image_Path']}")
                logger.info(f"  QR_Code_S3_URL: {df.iloc[row]['QR_Code_S3_URL']}")
                logger.info(f"  Image_S3_URL: {df.iloc[row]['Image_S3_URL']}")
                logger.info("---")
    else:
        logger.info("No duplicate image paths found!")

if __name__ == "__main__":
    check_duplicates() 