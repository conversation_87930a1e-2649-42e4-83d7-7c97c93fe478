#!/usr/bin/env python3
import csv
import re
import os

# Input and output file paths
INPUT_FILE = "scraped_recherche_data.csv"
OUTPUT_FILE = "filtered_recherche_data.csv"

# French indicators - words or patterns that suggest a record is in French
FRENCH_INDICATORS = [
    r'\bde la\b', r'\bdu\b', r'\bdes\b', r'\bau\b', r'\baux\b',
    r'\bà\b', r'\bpar\b', r'\bpour\b', r'\bavec\b', r'\bsans\b',
    r'\bchez\b', r'\bsous\b', r'\bsur\b', r'\bdans\b', r'\bentre\b',
    r'\bvers\b', r'\bcontre\b', r'\bpendant\b', r'\bdurant\b',
    r'\bselon\b', r'\bsauf\b', r'\bdevant\b', r'\bderrière\b',
    r'\bparmi\b', r'\bquant\b', r'\bvoici\b', r'\bvoilà\b',
    r'\bet\b', r'\bou\b', r'\bmais\b', r'\bdonc\b', r'\bcar\b',
    r'\bni\b', r'\bor\b', r'\ble\b', r'\bla\b', r'\bles\b',
    r'\bun\b', r'\bune\b', r'\bdes\b', r'\bce\b', r'\bcette\b',
    r'\bces\b', r'\bceux\b', r'\bcelles\b', r'\bmon\b', r'\bma\b',
    r'\bmes\b', r'\bton\b', r'\bta\b', r'\btes\b', r'\bson\b',
    r'\bsa\b', r'\bses\b', r'\bnotre\b', r'\bnos\b', r'\bvotre\b',
    r'\bvos\b', r'\bleur\b', r'\bleurs\b',
    'français', 'française', 'québec', 'québécois', 'québécoise',
    'montréal', 'québec', 'laval', 'gatineau', 'sherbrooke',
    'trois-rivières', 'chicoutimi', 'rimouski', 'rouyn-noranda',
    'baie-comeau', 'sept-îles', 'gaspé', 'joliette', 'drummondville',
    'saint-jean-sur-richelieu', 'saint-jérôme', 'saguenay', 'lévis',
    'shawinigan', 'alma', 'dolbeau-mistassini', 'matane', 'rivière-du-loup',
    'thetford mines', 'val-d\'or', 'amos', 'cowansville', 'magog',
    'saint-georges', 'sorel-tracy', 'victoriaville', 'granby',
    'saint-hyacinthe', 'salaberry-de-valleyfield', 'lachute',
    'sainte-agathe-des-monts', 'mont-laurier', 'sainte-marie',
    'montmagny', 'la malbaie', 'sainte-anne-des-monts', 'carleton-sur-mer',
    'new carlisle', 'percé', 'chandler', 'amqui', 'causapscal',
    'sainte-anne-de-beaupré', 'baie-saint-paul', 'la tuque',
    'saint-félicien', 'roberval', 'normandin', 'chibougamau',
    'lebel-sur-quévillon', 'matagami', 'fermont', 'havre-saint-pierre',
    'port-cartier', 'forestville', 'la sarre', 'senneterre',
    'sainte-adèle', 'sainte-marguerite-du-lac-masson', 'mont-tremblant',
    'sainte-agathe-des-monts', 'saint-sauveur', 'sainte-thérèse',
    'blainville', 'mirabel', 'saint-eustache', 'deux-montagnes',
    'sainte-marthe-sur-le-lac', 'boisbriand', 'rosemère', 'lorraine',
    'bois-des-filion', 'terrebonne', 'mascouche', 'repentigny',
    'l\'assomption', 'saint-sulpice', 'lavaltrie', 'lanoraie',
    'berthierville', 'saint-gabriel', 'louiseville', 'yamachiche',
    'trois-rivières', 'cap-de-la-madeleine', 'champlain', 'batiscan',
    'sainte-anne-de-la-pérade', 'deschambault-grondines', 'portneuf',
    'neuville', 'saint-augustin-de-desmaures', 'québec', 'beauport',
    'charlesbourg', 'loretteville', 'val-bélair', 'sainte-foy',
    'sillery', 'cap-rouge', 'saint-nicolas', 'lévis', 'lauzon',
    'saint-romuald', 'charny', 'saint-jean-chrysostome', 'saint-rédempteur',
    'saint-étienne-de-lauzon', 'pintendre', 'saint-henri', 'saint-anselme',
    'sainte-claire', 'saint-malachie', 'frampton', 'saint-elzéar',
    'sainte-marie', 'vallée-jonction', 'saint-joseph-de-beauce',
    'beauceville', 'saint-georges', 'lac-etchemin', 'sainte-justine',
    'saint-camille-de-lellis', 'saint-just-de-bretenières', 'saint-fabien-de-panet',
    'lac-frontière', 'sainte-lucie-de-beauregard', 'saint-adalbert',
    'saint-pamphile', 'saint-omer', 'saint-cyprien', 'sainte-rita',
    'saint-jean-de-dieu', 'saint-clément', 'saint-éloi', 'l\'isle-verte',
    'cacouna', 'rivière-du-loup', 'notre-dame-du-portage', 'kamouraska',
    'saint-pascal', 'la pocatière', 'saint-jean-port-joli', 'l\'islet',
    'saint-jean-de-l\'île-d\'orléans', 'sainte-famille', 'saint-françois-de-l\'île-d\'orléans',
    'saint-laurent-de-l\'île-d\'orléans', 'saint-pierre-de-l\'île-d\'orléans',
    'sainte-pétronille', 'château-richer', 'sainte-anne-de-beaupré',
    'beaupré', 'saint-ferréol-les-neiges', 'saint-tite-des-caps',
    'petite-rivière-saint-françois', 'baie-saint-paul', 'l\'isle-aux-coudres',
    'les éboulements', 'saint-irénée', 'la malbaie', 'clermont',
    'saint-aimé-des-lacs', 'notre-dame-des-monts', 'saint-urbain',
    'saint-hilarion', 'saint-siméon', 'baie-sainte-catherine',
    'tadoussac', 'sacré-cœur', 'les escoumins', 'les bergeronnes',
    'forestville', 'colombier', 'portneuf-sur-mer', 'longue-rive',
    'baie-comeau', 'pointe-lebel', 'pointe-aux-outardes', 'chute-aux-outardes',
    'ragueneau', 'franquelin', 'godbout', 'baie-trinité', 'port-cartier',
    'sept-îles', 'moisie', 'rivière-au-tonnerre', 'rivière-saint-jean',
    'longue-pointe-de-mingan', 'havre-saint-pierre', 'baie-johan-beetz',
    'aguanish', 'natashquan', 'kegaska', 'la romaine', 'harrington harbour',
    'tête-à-la-baleine', 'saint-augustin', 'bonne-espérance', 'blanc-sablon'
]

def is_french_record(record):
    """Check if a record is in French based on various indicators"""
    # Check language field
    if record.get('language of material') and 'fr' in record.get('language of material').lower():
        return True
    
    # Check title for French words
    title = record.get('name', '').lower()
    for indicator in FRENCH_INDICATORS:
        if re.search(r'\b' + indicator.lower() + r'\b', title):
            return True
    
    # Check other fields for French indicators
    fields_to_check = ['additional information', 'scope and content', 'subject heading']
    for field in fields_to_check:
        if field in record and record[field]:
            text = record[field].lower()
            # Count French words
            french_word_count = 0
            for indicator in FRENCH_INDICATORS:
                french_word_count += len(re.findall(r'\b' + indicator.lower() + r'\b', text))
            
            # If there are more than 3 French words, consider it a French record
            if french_word_count > 3:
                return True
    
    return False

def filter_french_records():
    """Filter out French records from the CSV file"""
    if not os.path.exists(INPUT_FILE):
        print(f"Error: Input file {INPUT_FILE} not found.")
        return
    
    total_records = 0
    french_records = 0
    english_records = 0
    
    try:
        with open(INPUT_FILE, 'r', encoding='utf-8') as infile, \
             open(OUTPUT_FILE, 'w', newline='', encoding='utf-8') as outfile:
            
            reader = csv.DictReader(infile)
            writer = csv.DictWriter(outfile, fieldnames=reader.fieldnames)
            writer.writeheader()
            
            for record in reader:
                total_records += 1
                
                if is_french_record(record):
                    french_records += 1
                    print(f"Skipping French record: {record.get('name', 'Unknown')}")
                else:
                    english_records += 1
                    writer.writerow(record)
                    
                # Print progress every 100 records
                if total_records % 100 == 0:
                    print(f"Processed {total_records} records...")
        
        print(f"\nFiltering complete!")
        print(f"Total records processed: {total_records}")
        print(f"French records removed: {french_records}")
        print(f"English records kept: {english_records}")
        print(f"Filtered data saved to: {OUTPUT_FILE}")
        
    except Exception as e:
        print(f"Error filtering records: {e}")

if __name__ == "__main__":
    filter_french_records()
