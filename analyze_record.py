#!/usr/bin/env python3
import requests
import re
from bs4 import BeautifulSoup
import json

def analyze_record(url):
    """Analyze a record page to extract metadata"""
    response = requests.get(url)
    html = response.text

    print(f"Analyzing URL: {url}")
    print("=" * 80)

    # Extract ecopy number from URL
    ecopy_match = re.search(r'ecopy=([^&]+)', url)
    if ecopy_match:
        ecopy = ecopy_match.group(1)
        print(f"Ecopy from URL: {ecopy}")

    # Extract title
    title_match = re.search(r'<title>(.*?)</title>', html)
    if title_match:
        title = title_match.group(1)
        if title.startswith("Collection search - "):
            title = title[len("Collection search - "):]
        print(f"Title: {title}")

    # Extract metadata fields
    print("\nMetadata fields:")

    # Look for hierarchical level
    level_match = re.search(r'Hierarchical level:?\s*</div>\s*<div[^>]*>\s*([^<]+)', html)
    if level_match:
        level = level_match.group(1).strip()
        print(f"Hierarchical level: {level}")

    # Look for date
    date_match = re.search(r'Date:?\s*</div>\s*<div[^>]*>\s*([^<]+)', html)
    if date_match:
        date = date_match.group(1).strip()
        print(f"Date: {date}")

    # Look for item number
    item_no_match = re.search(r'Item no\. \(creator\):?\s*</div>\s*<div[^>]*>\s*([^<]+)', html)
    if item_no_match:
        item_no = item_no_match.group(1).strip()
        print(f"Item no. (creator): {item_no}")

    # Look for type of material
    material_match = re.search(r'Type of material:?\s*</div>\s*<div[^>]*>\s*([^<]+)', html)
    if material_match:
        material = material_match.group(1).strip()
        print(f"Type of material: {material}")

    # Look for reference
    reference_match = re.search(r'Reference:?\s*</div>\s*<div[^>]*>\s*([^<]+)', html)
    if reference_match:
        reference = reference_match.group(1).strip()
        print(f"Reference: {reference}")

    # Look for found in
    found_in_match = re.search(r'Found in:?\s*</div>\s*<div[^>]*>\s*([^<]+)', html)
    if found_in_match:
        found_in = found_in_match.group(1).strip()
        print(f"Found in: {found_in}")

    # Look for extent
    extent_match = re.search(r'Extent:?\s*</div>\s*<div[^>]*>\s*([^<]+)', html)
    if extent_match:
        extent = extent_match.group(1).strip()
        print(f"Extent: {extent}")

    # Look for language
    language_match = re.search(r'Language of material:?\s*</div>\s*<div[^>]*>\s*([^<]+)', html)
    if language_match:
        language = language_match.group(1).strip()
        print(f"Language of material: {language}")

    # Look for additional names
    names_match = re.search(r'Additional name\(s\):?\s*</div>\s*<div[^>]*>\s*([^<]+)', html)
    if names_match:
        names = names_match.group(1).strip()
        print(f"Additional name(s): {names}")

    # Look for source
    source_match = re.search(r'Source:?\s*</div>\s*<div[^>]*>\s*([^<]+)', html)
    if source_match:
        source = source_match.group(1).strip()
        print(f"Source: {source}")

    # Look for subject heading
    subject_match = re.search(r'Subject heading:?\s*</div>\s*<div[^>]*>\s*([^<]+)', html)
    if subject_match:
        subject = subject_match.group(1).strip()
        print(f"Subject heading: {subject}")

    # Look for conditions of access
    access_match = re.search(r'Conditions of access:?\s*</div>\s*<div[^>]*>\s*([^<]+)', html)
    if access_match:
        access = access_match.group(1).strip()
        print(f"Conditions of access: {access}")

    # Look for terms of use
    terms_match = re.search(r'Terms of use:?\s*</div>\s*<div[^>]*>\s*([^<]+)', html)
    if terms_match:
        terms = terms_match.group(1).strip()
        print(f"Terms of use: {terms}")

    # Look for images
    print("\nImages:")
    img_matches = re.findall(r'<img[^>]*src="([^"]+)"[^>]*>', html)
    for i, img_src in enumerate(img_matches[:5]):  # Show first 5 images only
        print(f"  Image {i+1}: {img_src}")

    # Extract all metadata rows
    print("\nAll metadata rows:")
    metadata_rows = re.findall(r'<div[^>]*class="[^"]*metadata-label[^"]*"[^>]*>(.*?)</div>\s*<div[^>]*class="[^"]*metadata-value[^"]*"[^>]*>(.*?)</div>', html, re.DOTALL)
    for i, (label_html, value_html) in enumerate(metadata_rows):
        label = re.sub(r'<[^>]+>', '', label_html).strip()
        value = re.sub(r'<[^>]+>', '', value_html).strip()
        print(f"  Row {i+1}: {label} = {value}")

if __name__ == "__main__":
    # Analyze a sample record
    url = "https://recherche-collection-search.bac-lac.gc.ca/eng/Home/Record?app=fonandcol&IdNumber=4942854&q_type_1=q&q_1=Nunavut&ecopy=e010864242-v8"
    analyze_record(url)
