#!/usr/bin/env python3
"""
Test script to verify that the place field extraction fix works
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from scrape_nunavut_articles import extract_record_details, get_page

def test_place_extraction():
    """Test place extraction on a few sample URLs"""

    test_urls = [
        "https://recherche-collection-search.bac-lac.gc.ca/eng/Home/Record?app=fonandcol&IdNumber=5080778&q_type_1=q&q_1=Nunavut&ecopy=e010836176-v8",
        "https://recherche-collection-search.bac-lac.gc.ca/eng/Home/Record?app=fonandcol&IdNumber=5081121&q_type_1=q&q_1=Nunavut&ecopy=e010868837-v8",
        "https://recherche-collection-search.bac-lac.gc.ca/eng/Home/Record?app=fonandcol&IdNumber=4725697&q_type_1=q&q_1=Nunavut&ecopy=e011153909-v8"
    ]

    print("Testing place field extraction after fix...")
    print("=" * 60)

    for i, url in enumerate(test_urls, 1):
        print(f"\nTest {i}: {url}")
        print("-" * 60)

        try:
            # Get the HTML content first
            html = get_page(url)
            if not html:
                print("❌ FAILED: Could not fetch HTML content")
                continue

            # Extract metadata using the fixed scraper
            data = extract_record_details(html, url)

            # Check if place field is now populated
            place = data.get("place", "")
            place_of_creation = data.get("place of creation", "")

            # Debug: Let's also check what our regex would find directly
            import re
            place_match = re.search(r'<div[^>]*class="[^"]*CFCS-row-label[^"]*"[^>]*>Place:?\s*</div>\s*<div[^>]*class="[^"]*CFCS-row-value[^"]*"[^>]*>\s*([^<]+)', html, re.IGNORECASE | re.DOTALL)
            if place_match:
                direct_place = place_match.group(1).strip()
                print(f"Direct regex found: '{direct_place}'")
            else:
                print("Direct regex found nothing")

            print(f"Place field: '{place}'")
            print(f"Place of creation field: '{place_of_creation}'")

            if place:
                print("✅ SUCCESS: Place field is now populated!")
            else:
                print("❌ FAILED: Place field is still empty")

            # Also show a few other key fields for context
            print(f"Name: '{data.get('name', '')}'")
            print(f"Date: '{data.get('date', '')}'")
            print(f"Item ID: '{data.get('item id number', '')}'")

        except Exception as e:
            print(f"❌ ERROR: Failed to extract metadata: {e}")

        print()

if __name__ == "__main__":
    test_place_extraction()
