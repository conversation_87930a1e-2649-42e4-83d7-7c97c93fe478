#!/usr/bin/env python3
"""
Script to fix the place fields in filtered_recherche_data.csv by re-scraping each URL
"""
import csv
import time
import os
from scrape_nunavut_articles import get_page, extract_record_details

def fix_place_fields():
    """Read filtered_recherche_data.csv, re-scrape URLs to get correct place fields, and save updated file"""

    input_file = "filtered_recherche_data.csv"
    output_file = "filtered_recherche_data_fixed.csv"
    backup_file = "filtered_recherche_data_backup.csv"

    print("🔧 Starting place field correction process...")
    print("=" * 60)

    # Create backup of original file
    print(f"📋 Creating backup: {backup_file}")
    try:
        with open(input_file, 'r', encoding='utf-8') as src, open(backup_file, 'w', encoding='utf-8') as dst:
            dst.write(src.read())
        print("✅ Backup created successfully")
    except Exception as e:
        print(f"❌ Error creating backup: {e}")
        return

    # Read the filtered data
    print(f"📖 Reading {input_file}...")
    records = []
    fieldnames = []

    try:
        with open(input_file, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            fieldnames = reader.fieldnames
            records = list(reader)

        print(f"✅ Found {len(records)} records to process")

    except Exception as e:
        print(f"❌ Error reading input file: {e}")
        return

    # Process each record
    print("\n🔄 Processing records...")
    print("-" * 60)

    updated_count = 0
    error_count = 0
    save_frequency = 10  # Save progress every 10 records

    for i, record in enumerate(records, 1):
        url = record.get('url', '').strip()
        current_place = record.get('place', '').strip()

        print(f"\n[{i}/{len(records)}] Processing record {record.get('item id number', 'Unknown ID')}")
        print(f"URL: {url}")
        print(f"Current place: '{current_place}'")

        if not url:
            print("⚠️  No URL found, skipping...")
            error_count += 1
        else:
            try:
                # Get the HTML content
                html = get_page(url)
                if not html:
                    print("❌ Failed to fetch HTML content")
                    error_count += 1
                else:
                    # Extract metadata using our fixed scraper
                    scraped_data = extract_record_details(html, url)
                    new_place = scraped_data.get('place', '').strip()

                    print(f"New place: '{new_place}'")

                    # Update the place field if we got a better result
                    if new_place and new_place != current_place:
                        record['place'] = new_place
                        updated_count += 1
                        print("✅ Place field updated!")
                    elif new_place == current_place:
                        print("ℹ️  Place field already correct")
                    else:
                        print("⚠️  No place data found")

                # Be respectful to the server
                time.sleep(2)

            except Exception as e:
                print(f"❌ Error processing record: {e}")
                error_count += 1

        # Save progress every N records
        if i % save_frequency == 0 or i == len(records):
            print(f"\n💾 Saving progress... ({i}/{len(records)} processed)")
            try:
                with open(output_file, 'w', encoding='utf-8', newline='') as file:
                    writer = csv.DictWriter(file, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(records)
                print(f"✅ Progress saved! (Updated: {updated_count}, Errors: {error_count})")
            except Exception as e:
                print(f"❌ Error saving progress: {e}")
                # Continue processing even if save fails

    # Save the updated data
    print(f"\n💾 Saving updated data to {output_file}...")

    try:
        with open(output_file, 'w', encoding='utf-8', newline='') as file:
            writer = csv.DictWriter(file, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(records)

        print("✅ Updated data saved successfully!")

    except Exception as e:
        print(f"❌ Error saving updated data: {e}")
        return

    # Summary
    print("\n📊 SUMMARY")
    print("=" * 60)
    print(f"Total records processed: {len(records)}")
    print(f"Place fields updated: {updated_count}")
    print(f"Errors encountered: {error_count}")
    print(f"Success rate: {((len(records) - error_count) / len(records) * 100):.1f}%")

    if updated_count > 0:
        print(f"\n🎉 Successfully updated {updated_count} place fields!")
        print(f"📁 Updated data saved as: {output_file}")
        print(f"📁 Original data backed up as: {backup_file}")

        # Ask if user wants to replace the original file
        print(f"\n❓ Replace original {input_file} with corrected version? (y/n): ", end="")
        response = input().strip().lower()

        if response in ['y', 'yes']:
            try:
                os.replace(output_file, input_file)
                print(f"✅ Original file {input_file} has been updated with corrected place fields!")
            except Exception as e:
                print(f"❌ Error replacing original file: {e}")
        else:
            print(f"ℹ️  Corrected data remains in {output_file}")
    else:
        print("ℹ️  No place fields needed updating")

if __name__ == "__main__":
    fix_place_fields()
