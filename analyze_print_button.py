#!/usr/bin/env python3
import requests
import re
from bs4 import BeautifulSoup
import json

def analyze_record(url):
    """Analyze a record page to find print button and missing metadata"""
    response = requests.get(url)
    html = response.text
    soup = BeautifulSoup(html, 'html.parser')
    
    print(f"Analyzing URL: {url}")
    print("=" * 80)
    
    # Look for print button
    print("\nLooking for print button:")
    print_buttons = soup.select('a[href*="print"], button[onclick*="print"], a[onclick*="print"]')
    for btn in print_buttons:
        print(f"Print button found: {btn}")
    
    # Look for print links in JavaScript
    print("\nLooking for print links in JavaScript:")
    script_tags = soup.find_all('script')
    for script in script_tags:
        if script.string and 'print' in script.string.lower():
            print(f"Script with print functionality found: {script.string[:200]}...")
    
    # Look for image URLs
    print("\nLooking for image URLs:")
    img_tags = soup.find_all('img')
    for img in img_tags[:5]:  # Show first 5 images only
        print(f"Image found: {img.get('src', '')}")
    
    # Look for additional information
    print("\nLooking for additional information:")
    add_info_label = soup.find(string=lambda text: text and 'Additional information' in text)
    if add_info_label:
        print(f"Additional information label found: {add_info_label}")
        parent = add_info_label.parent
        next_elem = parent.find_next()
        if next_elem:
            print(f"Next element after label: {next_elem}")
    
    # Look for terms of use
    print("\nLooking for terms of use:")
    terms_label = soup.find(string=lambda text: text and 'Terms of use' in text)
    if terms_label:
        print(f"Terms of use label found: {terms_label}")
        parent = terms_label.parent
        next_elem = parent.find_next()
        if next_elem:
            print(f"Next element after label: {next_elem}")
    
    # Look for Other accession no
    print("\nLooking for Other accession no:")
    acc_no_label = soup.find(string=lambda text: text and 'Other accession no' in text)
    if acc_no_label:
        print(f"Other accession no label found: {acc_no_label}")
        parent = acc_no_label.parent
        next_elem = parent.find_next()
        if next_elem:
            print(f"Next element after label: {next_elem}")
    
    # Look for Conditions of access
    print("\nLooking for Conditions of access:")
    access_label = soup.find(string=lambda text: text and 'Conditions of access' in text)
    if access_label:
        print(f"Conditions of access label found: {access_label}")
        parent = access_label.parent
        next_elem = parent.find_next()
        if next_elem:
            print(f"Next element after label: {next_elem}")
    
    # Look for metadata in HTML structure
    print("\nLooking for metadata in HTML structure:")
    metadata_rows = soup.select('.metadata-row, .row')
    for row in metadata_rows:
        label_elem = row.select_one('.metadata-label, .label, th')
        value_elem = row.select_one('.metadata-value, .value, td')
        if label_elem and value_elem:
            label = label_elem.text.strip()
            value = value_elem.text.strip()
            print(f"{label}: {value}")
    
    # Look for specific patterns in HTML
    print("\nLooking for specific patterns in HTML:")
    patterns = [
        r'Additional information:?\s*</div>\s*<div[^>]*>\s*([^<]+)',
        r'Terms of use:?\s*</div>\s*<div[^>]*>\s*([^<]+)',
        r'Other accession no:?\s*</div>\s*<div[^>]*>\s*([^<]+)',
        r'Conditions of access:?\s*</div>\s*<div[^>]*>\s*([^<]+)',
        r'print\("([^"]+)"',
        r'window\.open\("([^"]+)"',
        r'href="([^"]+print[^"]*)"'
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, html, re.IGNORECASE)
        if matches:
            print(f"Pattern '{pattern}' found: {matches}")

if __name__ == "__main__":
    # Analyze a sample record
    url = "https://recherche-collection-search.bac-lac.gc.ca/eng/Home/Record?app=fonandcol&IdNumber=4942854&q_type_1=q&q_1=Nunavut&ecopy=e010864242-v8"
    analyze_record(url)
